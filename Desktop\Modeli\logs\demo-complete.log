[2025-06-03 03:31:24] [INFO] Stratégie active: <PERSON><PERSON><PERSON> (logs/demo-complete.log)
[2025-06-03 03:31:24] [INFO] Action utilisateur: Configuration - Stratégie de logging changée vers: <PERSON><PERSON><PERSON> (logs/demo-complete.log)
[2025-06-03 03:31:24] [INFO] Action utilisateur: Sélection - Type de forme sélectionné: Rectangle
[2025-06-03 03:31:24] [INFO] Action utilisateur: Dessin - Forme créée: Rectangle de Point(1,00, 1,00) à Point(5,00, 4,00)
[2025-06-03 03:31:24] [INFO] Action utilisateur: Sélection - Type de forme sélectionné: Cercle
[2025-06-03 03:31:24] [INFO] Action utilisateur: Dessin - Forme créée: Cercle de Point(3,00, 3,00) à Point(6,00, 6,00)
[2025-06-03 03:31:24] [INFO] Action utilisateur: Sélection - Type de forme sélectionné: Ligne
[2025-06-03 03:31:24] [INFO] Action utilisateur: Dessin - Forme créée: Ligne de Point(0,00, 0,00) à Point(8,00, 6,00)
[2025-06-03 03:31:24] [INFO] Action utilisateur: Annulation - Commande annulée: Ajouter Line: Line[start=Point(0,00, 0,00), end=Point(8,00, 6,00), length=10,00]
[2025-06-03 03:31:24] [INFO] Action utilisateur: Annulation - Commande annulée: Ajouter Circle: Circle[center=Point(3,00, 3,00), radius=4,24]
[2025-06-03 03:31:24] [INFO] Action utilisateur: Rétablissement - Commande rétablie: Ajouter Circle: Circle[center=Point(3,00, 3,00), radius=4,24]
[2025-06-03 03:31:24] [INFO] Action utilisateur: Sélection - Type de forme sélectionné: Cercle
[2025-06-03 03:31:24] [INFO] Action utilisateur: Dessin - Forme créée: Cercle de Point(7,00, 2,00) à Point(9,00, 4,00)
[2025-06-03 03:31:24] [INFO] Action utilisateur: Sélection - Type de forme sélectionné: Rectangle
[2025-06-03 03:31:24] [INFO] Action utilisateur: Dessin - Forme créée: Rectangle de Point(2,00, 6,00) à Point(4,00, 8,00)
[2025-06-03 03:31:24] [INFO] Action utilisateur: Sauvegarde - Dessin sauvegardé: Mon Premier Dessin
[2025-06-03 03:31:24] [INFO] Action utilisateur: Effacement - Toutes les formes ont été supprimées
[2025-06-03 03:31:24] [INFO] Action utilisateur: Annulation - Commande annulée: Effacer tout le dessin
[2025-06-03 03:31:24] [INFO] Changement de stratégie vers: Mémoire
[2025-06-03 03:34:12] [INFO] Stratégie active: Fichier (logs/demo-complete.log)
[2025-06-03 03:34:12] [INFO] Action utilisateur: Configuration - Stratégie de logging changée vers: Fichier (logs/demo-complete.log)
[2025-06-03 03:34:12] [INFO] Action utilisateur: Sélection - Type de forme sélectionné: Rectangle
[2025-06-03 03:34:12] [INFO] Action utilisateur: Dessin - Forme créée: Rectangle de Point(1,00, 1,00) à Point(5,00, 4,00)
[2025-06-03 03:34:12] [INFO] Action utilisateur: Sélection - Type de forme sélectionné: Cercle
[2025-06-03 03:34:12] [INFO] Action utilisateur: Dessin - Forme créée: Cercle de Point(3,00, 3,00) à Point(6,00, 6,00)
[2025-06-03 03:34:12] [INFO] Action utilisateur: Sélection - Type de forme sélectionné: Ligne
[2025-06-03 03:34:12] [INFO] Action utilisateur: Dessin - Forme créée: Ligne de Point(0,00, 0,00) à Point(8,00, 6,00)
[2025-06-03 03:34:12] [INFO] Action utilisateur: Annulation - Commande annulée: Ajouter Line: Line[start=Point(0,00, 0,00), end=Point(8,00, 6,00), length=10,00]
[2025-06-03 03:34:12] [INFO] Action utilisateur: Annulation - Commande annulée: Ajouter Circle: Circle[center=Point(3,00, 3,00), radius=4,24]
[2025-06-03 03:34:12] [INFO] Action utilisateur: Rétablissement - Commande rétablie: Ajouter Circle: Circle[center=Point(3,00, 3,00), radius=4,24]
[2025-06-03 03:34:12] [INFO] Action utilisateur: Sélection - Type de forme sélectionné: Cercle
[2025-06-03 03:34:12] [INFO] Action utilisateur: Dessin - Forme créée: Cercle de Point(7,00, 2,00) à Point(9,00, 4,00)
[2025-06-03 03:34:12] [INFO] Action utilisateur: Sélection - Type de forme sélectionné: Rectangle
[2025-06-03 03:34:12] [INFO] Action utilisateur: Dessin - Forme créée: Rectangle de Point(2,00, 6,00) à Point(4,00, 8,00)
[2025-06-03 03:34:12] [INFO] Action utilisateur: Sauvegarde - Dessin sauvegardé: Mon Premier Dessin
[2025-06-03 03:34:12] [INFO] Action utilisateur: Effacement - Toutes les formes ont été supprimées
[2025-06-03 03:34:12] [INFO] Action utilisateur: Annulation - Commande annulée: Effacer tout le dessin
[2025-06-03 03:34:12] [INFO] Changement de stratégie vers: Mémoire
[2025-06-03 03:38:23] [INFO] Stratégie active: Fichier (logs/demo-complete.log)
[2025-06-03 03:38:23] [INFO] Action utilisateur: Configuration - Stratégie de logging changée vers: Fichier (logs/demo-complete.log)
[2025-06-03 03:38:23] [INFO] Action utilisateur: Sélection - Type de forme sélectionné: Rectangle
[2025-06-03 03:38:23] [INFO] Action utilisateur: Dessin - Forme créée: Rectangle de Point(1,00, 1,00) à Point(5,00, 4,00)
[2025-06-03 03:38:23] [INFO] Action utilisateur: Sélection - Type de forme sélectionné: Cercle
[2025-06-03 03:38:23] [INFO] Action utilisateur: Dessin - Forme créée: Cercle de Point(3,00, 3,00) à Point(6,00, 6,00)
[2025-06-03 03:38:23] [INFO] Action utilisateur: Sélection - Type de forme sélectionné: Ligne
[2025-06-03 03:38:23] [INFO] Action utilisateur: Dessin - Forme créée: Ligne de Point(0,00, 0,00) à Point(8,00, 6,00)
[2025-06-03 03:38:23] [INFO] Action utilisateur: Annulation - Commande annulée: Ajouter Line: Line[start=Point(0,00, 0,00), end=Point(8,00, 6,00), length=10,00]
[2025-06-03 03:38:23] [INFO] Action utilisateur: Annulation - Commande annulée: Ajouter Circle: Circle[center=Point(3,00, 3,00), radius=4,24]
[2025-06-03 03:38:23] [INFO] Action utilisateur: Rétablissement - Commande rétablie: Ajouter Circle: Circle[center=Point(3,00, 3,00), radius=4,24]
[2025-06-03 03:38:23] [INFO] Action utilisateur: Sélection - Type de forme sélectionné: Cercle
[2025-06-03 03:38:23] [INFO] Action utilisateur: Dessin - Forme créée: Cercle de Point(7,00, 2,00) à Point(9,00, 4,00)
[2025-06-03 03:38:23] [INFO] Action utilisateur: Sélection - Type de forme sélectionné: Rectangle
[2025-06-03 03:38:23] [INFO] Action utilisateur: Dessin - Forme créée: Rectangle de Point(2,00, 6,00) à Point(4,00, 8,00)
[2025-06-03 03:38:23] [INFO] Action utilisateur: Sauvegarde - Dessin sauvegardé: Mon Premier Dessin
[2025-06-03 03:38:23] [INFO] Action utilisateur: Effacement - Toutes les formes ont été supprimées
[2025-06-03 03:38:23] [INFO] Action utilisateur: Annulation - Commande annulée: Effacer tout le dessin
[2025-06-03 03:38:23] [INFO] Changement de stratégie vers: Mémoire
[2025-06-03 03:39:06] [INFO] Stratégie active: Fichier (logs/demo-complete.log)
[2025-06-03 03:39:06] [INFO] Action utilisateur: Configuration - Stratégie de logging changée vers: Fichier (logs/demo-complete.log)
[2025-06-03 03:39:06] [INFO] Action utilisateur: Sélection - Type de forme sélectionné: Rectangle
[2025-06-03 03:39:07] [INFO] Action utilisateur: Dessin - Forme créée: Rectangle de Point(1,00, 1,00) à Point(5,00, 4,00)
[2025-06-03 03:39:07] [INFO] Action utilisateur: Sélection - Type de forme sélectionné: Cercle
[2025-06-03 03:39:07] [INFO] Action utilisateur: Dessin - Forme créée: Cercle de Point(3,00, 3,00) à Point(6,00, 6,00)
[2025-06-03 03:39:07] [INFO] Action utilisateur: Sélection - Type de forme sélectionné: Ligne
[2025-06-03 03:39:07] [INFO] Action utilisateur: Dessin - Forme créée: Ligne de Point(0,00, 0,00) à Point(8,00, 6,00)
[2025-06-03 03:39:07] [INFO] Action utilisateur: Annulation - Commande annulée: Ajouter Line: Line[start=Point(0,00, 0,00), end=Point(8,00, 6,00), length=10,00]
[2025-06-03 03:39:07] [INFO] Action utilisateur: Annulation - Commande annulée: Ajouter Circle: Circle[center=Point(3,00, 3,00), radius=4,24]
[2025-06-03 03:39:07] [INFO] Action utilisateur: Rétablissement - Commande rétablie: Ajouter Circle: Circle[center=Point(3,00, 3,00), radius=4,24]
[2025-06-03 03:39:07] [INFO] Action utilisateur: Sélection - Type de forme sélectionné: Cercle
[2025-06-03 03:39:07] [INFO] Action utilisateur: Dessin - Forme créée: Cercle de Point(7,00, 2,00) à Point(9,00, 4,00)
[2025-06-03 03:39:07] [INFO] Action utilisateur: Sélection - Type de forme sélectionné: Rectangle
[2025-06-03 03:39:07] [INFO] Action utilisateur: Dessin - Forme créée: Rectangle de Point(2,00, 6,00) à Point(4,00, 8,00)
[2025-06-03 03:39:07] [INFO] Action utilisateur: Sauvegarde - Dessin sauvegardé: Mon Premier Dessin
[2025-06-03 03:39:07] [INFO] Action utilisateur: Effacement - Toutes les formes ont été supprimées
[2025-06-03 03:39:07] [INFO] Action utilisateur: Annulation - Commande annulée: Effacer tout le dessin
[2025-06-03 03:39:07] [INFO] Changement de stratégie vers: Mémoire
