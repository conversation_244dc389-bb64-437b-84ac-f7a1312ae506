package com.drawing.model;

import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

/**
 * Interface pour toutes les formes géométriques
 * Utilise le pattern Strategy pour le rendu
 */
public interface Shape {
    
    /**
     * Dessine la forme sur le contexte graphique
     */
    void draw(GraphicsContext gc);
    
    /**
     * Retourne le type de la forme
     */
    String getType();
    
    /**
     * Retourne la couleur de la forme
     */
    Color getColor();
    
    /**
     * Définit la couleur de la forme
     */
    void setColor(Color color);
    
    /**
     * Vérifie si un point est contenu dans la forme
     */
    boolean contains(Point point);
    
    /**
     * Retourne les limites de la forme (bounding box)
     */
    Bounds getBounds();
    
    /**
     * Clone la forme
     */
    Shape clone();
}
