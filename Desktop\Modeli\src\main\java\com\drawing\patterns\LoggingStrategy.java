package com.drawing.patterns;

/**
 * Strategy Pattern pour la journalisation
 * Permet de changer dynamiquement la stratégie de journalisation
 */
public interface LoggingStrategy {
    
    /**
     * Enregistre un message de log
     */
    void log(String message);
    
    /**
     * Enregistre un message de log avec un niveau
     */
    void log(LogLevel level, String message);
    
    /**
     * Enregistre un message de log avec une exception
     */
    void log(LogLevel level, String message, Throwable throwable);
    
    /**
     * Ferme les ressources utilisées par la stratégie de logging
     */
    void close();
    
    /**
     * Retourne le nom de la stratégie
     */
    String getStrategyName();
    
    /**
     * Vérifie si la stratégie est disponible
     */
    boolean isAvailable();
    
    /**
     * Niveaux de log
     */
    enum LogLevel {
        DEBUG("DEBUG"),
        INFO("INFO"),
        WARN("WARN"),
        ERROR("ERROR");
        
        private final String name;
        
        LogLevel(String name) {
            this.name = name;
        }
        
        public String getName() {
            return name;
        }
        
        @Override
        public String toString() {
            return name;
        }
    }
}
