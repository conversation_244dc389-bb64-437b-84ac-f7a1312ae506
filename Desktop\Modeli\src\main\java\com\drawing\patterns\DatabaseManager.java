package com.drawing.patterns;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Singleton Pattern pour gérer la connexion à la base de données SQLite
 */
public class DatabaseManager {
    
    private static DatabaseManager instance;
    private Connection connection;
    private final String databasePath;
    
    private DatabaseManager() {
        this.databasePath = "data/drawing_app.db";
        initializeDatabase();
    }
    
    /**
     * Retourne l'instance unique du DatabaseManager
     */
    public static synchronized DatabaseManager getInstance() {
        if (instance == null) {
            instance = new DatabaseManager();
        }
        return instance;
    }
    
    /**
     * Initialise la base de données et crée les tables nécessaires
     */
    private void initializeDatabase() {
        try {
            // Créer le répertoire data s'il n'existe pas
            Path dataDir = Paths.get("data");
            if (!Files.exists(dataDir)) {
                Files.createDirectories(dataDir);
            }
            
            // Établir la connexion
            String url = "jdbc:sqlite:" + databasePath;
            connection = DriverManager.getConnection(url);
            
            // Créer les tables
            createTables();
            
            System.out.println("Base de données initialisée: " + databasePath);
            
        } catch (Exception e) {
            System.err.println("Erreur lors de l'initialisation de la base de données: " + e.getMessage());
            connection = null;
        }
    }
    
    /**
     * Crée les tables nécessaires
     */
    private void createTables() throws SQLException {
        try (Statement stmt = connection.createStatement()) {
            
            // Table pour les dessins
            String createDrawingsTable = """
                CREATE TABLE IF NOT EXISTS drawings (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    data TEXT NOT NULL,
                    created_at DATETIME NOT NULL,
                    modified_at DATETIME NOT NULL
                )
                """;
            stmt.executeUpdate(createDrawingsTable);
            
            // Table pour les logs (sera créée par DatabaseLogger si nécessaire)
            String createLogsTable = """
                CREATE TABLE IF NOT EXISTS logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME NOT NULL,
                    level VARCHAR(10) NOT NULL,
                    message TEXT NOT NULL,
                    exception TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
                """;
            stmt.executeUpdate(createLogsTable);
            
            // Index pour améliorer les performances
            String createDrawingsIndex = """
                CREATE INDEX IF NOT EXISTS idx_drawings_name ON drawings(name)
                """;
            stmt.executeUpdate(createDrawingsIndex);
            
            String createLogsIndex = """
                CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON logs(timestamp)
                """;
            stmt.executeUpdate(createLogsIndex);
        }
    }
    
    /**
     * Retourne la connexion à la base de données
     */
    public Connection getConnection() {
        try {
            // Vérifier si la connexion est toujours valide
            if (connection == null || connection.isClosed()) {
                initializeDatabase();
            }
        } catch (SQLException e) {
            System.err.println("Erreur lors de la vérification de la connexion: " + e.getMessage());
            return null;
        }
        return connection;
    }
    
    /**
     * Vérifie si la connexion est active
     */
    public boolean isConnected() {
        try {
            return connection != null && !connection.isClosed();
        } catch (SQLException e) {
            return false;
        }
    }
    
    /**
     * Ferme la connexion à la base de données
     */
    public void closeConnection() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
                System.out.println("Connexion à la base de données fermée");
            }
        } catch (SQLException e) {
            System.err.println("Erreur lors de la fermeture de la connexion: " + e.getMessage());
        }
    }
    
    /**
     * Exécute une requête de mise à jour (INSERT, UPDATE, DELETE)
     */
    public boolean executeUpdate(String sql, Object... parameters) {
        try (PreparedStatement stmt = getConnection().prepareStatement(sql)) {
            for (int i = 0; i < parameters.length; i++) {
                stmt.setObject(i + 1, parameters[i]);
            }
            stmt.executeUpdate();
            return true;
        } catch (SQLException e) {
            System.err.println("Erreur lors de l'exécution de la requête: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Démarre une transaction
     */
    public void beginTransaction() throws SQLException {
        if (connection != null) {
            connection.setAutoCommit(false);
        }
    }
    
    /**
     * Valide une transaction
     */
    public void commitTransaction() throws SQLException {
        if (connection != null) {
            connection.commit();
            connection.setAutoCommit(true);
        }
    }
    
    /**
     * Annule une transaction
     */
    public void rollbackTransaction() throws SQLException {
        if (connection != null) {
            connection.rollback();
            connection.setAutoCommit(true);
        }
    }
    
    public String getDatabasePath() {
        return databasePath;
    }
}
