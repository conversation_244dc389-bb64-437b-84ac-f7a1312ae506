package com.drawing.model;

/**
 * Tests simples pour PointSimple sans dépendances JUnit
 * Démontre les tests manuels
 */
public class PointSimpleTest {
    
    private static int testCount = 0;
    private static int passedTests = 0;
    
    public static void main(String[] args) {
        System.out.println("=== Tests pour PointSimple ===");
        
        testConstructor();
        testGetters();
        testDistanceTo();
        testEquals();
        testHashCode();
        testToString();
        testDistanceCalculation();
        testNegativeCoordinates();
        testDecimalCoordinates();
        
        System.out.println("\n=== Résultats des tests ===");
        System.out.println("Tests exécutés: " + testCount);
        System.out.println("Tests réussis: " + passedTests);
        System.out.println("Tests échoués: " + (testCount - passedTests));
        
        if (passedTests == testCount) {
            System.out.println("✅ Tous les tests sont passés!");
        } else {
            System.out.println("❌ Certains tests ont échoué");
        }
    }
    
    static void testConstructor() {
        System.out.print("Test Constructor... ");
        try {
            PointSimple p = new PointSimple(5.5, -3.2);
            assert Math.abs(p.getX() - 5.5) < 0.001 : "X coordinate incorrect";
            assert Math.abs(p.getY() - (-3.2)) < 0.001 : "Y coordinate incorrect";
            passed();
        } catch (Exception e) {
            failed(e);
        }
    }
    
    static void testGetters() {
        System.out.print("Test Getters... ");
        try {
            PointSimple origin = new PointSimple(0, 0);
            PointSimple point1 = new PointSimple(3, 4);
            
            assert origin.getX() == 0 : "Origin X should be 0";
            assert origin.getY() == 0 : "Origin Y should be 0";
            assert point1.getX() == 3 : "Point1 X should be 3";
            assert point1.getY() == 4 : "Point1 Y should be 4";
            passed();
        } catch (Exception e) {
            failed(e);
        }
    }
    
    static void testDistanceTo() {
        System.out.print("Test DistanceTo... ");
        try {
            PointSimple origin = new PointSimple(0, 0);
            PointSimple point1 = new PointSimple(3, 4);
            
            // Distance de l'origine au point (3,4) doit être 5
            double distance = origin.distanceTo(point1);
            assert Math.abs(distance - 5.0) < 0.001 : "Distance should be 5.0, got " + distance;
            
            // Distance d'un point à lui-même doit être 0
            assert point1.distanceTo(point1) == 0.0 : "Distance to self should be 0";
            
            passed();
        } catch (Exception e) {
            failed(e);
        }
    }
    
    static void testEquals() {
        System.out.print("Test Equals... ");
        try {
            PointSimple origin = new PointSimple(0, 0);
            PointSimple sameAsOrigin = new PointSimple(0, 0);
            PointSimple different = new PointSimple(1, 1);
            
            assert origin.equals(sameAsOrigin) : "Equal points should be equal";
            assert !origin.equals(different) : "Different points should not be equal";
            assert !origin.equals(null) : "Point should not equal null";
            assert origin.equals(origin) : "Point should equal itself";
            
            passed();
        } catch (Exception e) {
            failed(e);
        }
    }
    
    static void testHashCode() {
        System.out.print("Test HashCode... ");
        try {
            PointSimple origin = new PointSimple(0, 0);
            PointSimple sameAsOrigin = new PointSimple(0, 0);
            
            assert origin.hashCode() == sameAsOrigin.hashCode() : "Equal objects should have same hashCode";
            passed();
        } catch (Exception e) {
            failed(e);
        }
    }
    
    static void testToString() {
        System.out.print("Test ToString... ");
        try {
            PointSimple origin = new PointSimple(0, 0);
            String str = origin.toString();
            
            assert str != null : "toString should not return null";
            assert str.contains("0") : "toString should contain coordinates";
            assert str.contains("Point") : "toString should contain 'Point'";
            
            passed();
        } catch (Exception e) {
            failed(e);
        }
    }
    
    static void testDistanceCalculation() {
        System.out.print("Test Distance Calculation... ");
        try {
            PointSimple p1 = new PointSimple(0, 0);
            PointSimple p2 = new PointSimple(3, 4);
            PointSimple p3 = new PointSimple(6, 8);
            
            // Triangle 3-4-5
            assert Math.abs(p1.distanceTo(p2) - 5.0) < 0.001 : "Distance should be 5.0";
            
            // Distance doublée
            assert Math.abs(p1.distanceTo(p3) - 10.0) < 0.001 : "Distance should be 10.0";
            
            passed();
        } catch (Exception e) {
            failed(e);
        }
    }
    
    static void testNegativeCoordinates() {
        System.out.print("Test Negative Coordinates... ");
        try {
            PointSimple origin = new PointSimple(0, 0);
            PointSimple negative = new PointSimple(-5, -12);
            
            assert Math.abs(origin.distanceTo(negative) - 13.0) < 0.001 : "Distance should be 13.0";
            passed();
        } catch (Exception e) {
            failed(e);
        }
    }
    
    static void testDecimalCoordinates() {
        System.out.print("Test Decimal Coordinates... ");
        try {
            PointSimple decimal1 = new PointSimple(1.5, 2.5);
            PointSimple decimal2 = new PointSimple(4.5, 6.5);
            
            double expectedDistance = Math.sqrt(9 + 16); // sqrt((4.5-1.5)² + (6.5-2.5)²)
            double actualDistance = decimal1.distanceTo(decimal2);
            
            assert Math.abs(actualDistance - expectedDistance) < 0.001 : 
                "Distance should be " + expectedDistance + ", got " + actualDistance;
            
            passed();
        } catch (Exception e) {
            failed(e);
        }
    }
    
    static void passed() {
        System.out.println("✅ PASSÉ");
        testCount++;
        passedTests++;
    }
    
    static void failed(Exception e) {
        System.out.println("❌ ÉCHOUÉ: " + e.getMessage());
        testCount++;
    }
}
