package com.drawing.patterns;

import com.drawing.model.*;
import javafx.scene.paint.Color;

/**
 * Factory Pattern pour créer des formes géométriques
 * Centralise la création des formes et facilite l'ajout de nouveaux types
 */
public class ShapeFactory {
    
    public enum ShapeType {
        RECTANGLE("Rectangle"),
        CIRCLE("Cercle"),
        LINE("Ligne");
        
        private final String displayName;
        
        ShapeType(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }

    /**
     * Crée une forme rectangle
     */
    public static Shape createRectangle(Point topLeft, Point bottomRight, Color color) {
        double width = Math.abs(bottomRight.getX() - topLeft.getX());
        double height = Math.abs(bottomRight.getY() - topLeft.getY());
        
        // Assure que topLeft est vraiment en haut à gauche
        double actualTopLeftX = Math.min(topLeft.getX(), bottomRight.getX());
        double actualTopLeftY = Math.min(topLeft.getY(), bottomRight.getY());
        Point actualTopLeft = new Point(actualTopLeftX, actualTopLeftY);
        
        return new Rectangle(actualTopLeft, width, height, color);
    }

    /**
     * Crée une forme rectangle avec couleur par défaut
     */
    public static Shape createRectangle(Point topLeft, Point bottomRight) {
        return createRectangle(topLeft, bottomRight, Color.BLACK);
    }

    /**
     * Crée une forme cercle
     */
    public static Shape createCircle(Point center, Point edgePoint, Color color) {
        double radius = center.distanceTo(edgePoint);
        return new Circle(center, radius, color);
    }

    /**
     * Crée une forme cercle avec couleur par défaut
     */
    public static Shape createCircle(Point center, Point edgePoint) {
        return createCircle(center, edgePoint, Color.BLACK);
    }

    /**
     * Crée une forme ligne
     */
    public static Shape createLine(Point start, Point end, Color color) {
        return new Line(start, end, color);
    }

    /**
     * Crée une forme ligne avec couleur par défaut
     */
    public static Shape createLine(Point start, Point end) {
        return createLine(start, end, Color.BLACK);
    }

    /**
     * Crée une forme basée sur le type et deux points
     */
    public static Shape createShape(ShapeType type, Point point1, Point point2, Color color) {
        switch (type) {
            case RECTANGLE:
                return createRectangle(point1, point2, color);
            case CIRCLE:
                return createCircle(point1, point2, color);
            case LINE:
                return createLine(point1, point2, color);
            default:
                throw new IllegalArgumentException("Type de forme non supporté: " + type);
        }
    }

    /**
     * Crée une forme basée sur le type et deux points avec couleur par défaut
     */
    public static Shape createShape(ShapeType type, Point point1, Point point2) {
        return createShape(type, point1, point2, Color.BLACK);
    }

    /**
     * Crée une forme basée sur une chaîne de caractères
     */
    public static Shape createShape(String typeString, Point point1, Point point2, Color color) {
        ShapeType type = parseShapeType(typeString);
        return createShape(type, point1, point2, color);
    }

    /**
     * Parse une chaîne en ShapeType
     */
    public static ShapeType parseShapeType(String typeString) {
        if (typeString == null) {
            throw new IllegalArgumentException("Le type de forme ne peut pas être null");
        }
        
        switch (typeString.toLowerCase()) {
            case "rectangle":
            case "rect":
                return ShapeType.RECTANGLE;
            case "circle":
            case "cercle":
                return ShapeType.CIRCLE;
            case "line":
            case "ligne":
                return ShapeType.LINE;
            default:
                throw new IllegalArgumentException("Type de forme non reconnu: " + typeString);
        }
    }

    /**
     * Retourne tous les types de formes disponibles
     */
    public static ShapeType[] getAvailableShapeTypes() {
        return ShapeType.values();
    }

    /**
     * Vérifie si un type de forme est valide
     */
    public static boolean isValidShapeType(String typeString) {
        try {
            parseShapeType(typeString);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}
