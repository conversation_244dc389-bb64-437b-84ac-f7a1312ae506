package com.drawing.service;

import com.drawing.patterns.LoggingStrategy;
import com.drawing.patterns.LoggingStrategy.LogLevel;
import com.drawing.patterns.ConsoleLogger;
import com.drawing.patterns.FileLogger;
import com.drawing.patterns.DatabaseLogger;

import java.util.ArrayList;
import java.util.List;

/**
 * Service de journalisation utilisant le pattern Strategy
 * Permet de changer dynamiquement la stratégie de logging
 */
public class LoggingService {
    
    private LoggingStrategy currentStrategy;
    private final List<LoggingStrategy> availableStrategies;
    
    public LoggingService() {
        this.availableStrategies = new ArrayList<>();
        initializeStrategies();
        
        // Par défaut, utiliser la console
        setStrategy(StrategyType.CONSOLE);
    }
    
    /**
     * Types de stratégies disponibles
     */
    public enum StrategyType {
        CONSOLE("Console"),
        FILE("Fichier"),
        DATABASE("Base de données");
        
        private final String displayName;
        
        StrategyType(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
    
    /**
     * Initialise toutes les stratégies disponibles
     */
    private void initializeStrategies() {
        availableStrategies.add(new ConsoleLogger());
        availableStrategies.add(new FileLogger());
        availableStrategies.add(new DatabaseLogger());
    }
    
    /**
     * Change la stratégie de logging
     */
    public boolean setStrategy(StrategyType type) {
        LoggingStrategy newStrategy = getStrategyByType(type);
        if (newStrategy != null && newStrategy.isAvailable()) {
            if (currentStrategy != null) {
                log(LogLevel.INFO, "Changement de stratégie de logging vers: " + newStrategy.getStrategyName());
            }
            currentStrategy = newStrategy;
            log(LogLevel.INFO, "Stratégie de logging active: " + currentStrategy.getStrategyName());
            return true;
        }
        return false;
    }
    
    /**
     * Retourne la stratégie correspondant au type
     */
    private LoggingStrategy getStrategyByType(StrategyType type) {
        switch (type) {
            case CONSOLE:
                return availableStrategies.stream()
                    .filter(s -> s instanceof ConsoleLogger)
                    .findFirst()
                    .orElse(null);
            case FILE:
                return availableStrategies.stream()
                    .filter(s -> s instanceof FileLogger)
                    .findFirst()
                    .orElse(null);
            case DATABASE:
                return availableStrategies.stream()
                    .filter(s -> s instanceof DatabaseLogger)
                    .findFirst()
                    .orElse(null);
            default:
                return null;
        }
    }
    
    /**
     * Log un message avec le niveau INFO
     */
    public void log(String message) {
        log(LogLevel.INFO, message);
    }
    
    /**
     * Log un message avec un niveau spécifique
     */
    public void log(LogLevel level, String message) {
        if (currentStrategy != null) {
            currentStrategy.log(level, message);
        }
    }
    
    /**
     * Log un message avec une exception
     */
    public void log(LogLevel level, String message, Throwable throwable) {
        if (currentStrategy != null) {
            currentStrategy.log(level, message, throwable);
        }
    }
    
    /**
     * Log les actions utilisateur
     */
    public void logUserAction(String action, String details) {
        log(LogLevel.INFO, String.format("Action utilisateur: %s - %s", action, details));
    }
    
    /**
     * Log les erreurs
     */
    public void logError(String message, Throwable throwable) {
        log(LogLevel.ERROR, message, throwable);
    }
    
    /**
     * Log les avertissements
     */
    public void logWarning(String message) {
        log(LogLevel.WARN, message);
    }
    
    /**
     * Log les informations de debug
     */
    public void logDebug(String message) {
        log(LogLevel.DEBUG, message);
    }
    
    /**
     * Retourne la stratégie actuelle
     */
    public LoggingStrategy getCurrentStrategy() {
        return currentStrategy;
    }
    
    /**
     * Retourne le nom de la stratégie actuelle
     */
    public String getCurrentStrategyName() {
        return currentStrategy != null ? currentStrategy.getStrategyName() : "Aucune";
    }
    
    /**
     * Retourne les types de stratégies disponibles
     */
    public StrategyType[] getAvailableStrategyTypes() {
        return StrategyType.values();
    }
    
    /**
     * Vérifie si une stratégie est disponible
     */
    public boolean isStrategyAvailable(StrategyType type) {
        LoggingStrategy strategy = getStrategyByType(type);
        return strategy != null && strategy.isAvailable();
    }
    
    /**
     * Ferme le service de logging
     */
    public void close() {
        if (currentStrategy != null) {
            log(LogLevel.INFO, "Fermeture du service de logging");
            currentStrategy.close();
        }
        
        // Fermer toutes les stratégies
        for (LoggingStrategy strategy : availableStrategies) {
            if (strategy != currentStrategy) {
                strategy.close();
            }
        }
    }
}
