package com.drawing.standalone;

import javax.swing.*;
import java.awt.*;
import java.awt.event.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Application Swing autonome pour dessiner des formes géométriques
 * Démontre les design patterns sans aucune dépendance externe
 */
public class StandaloneDrawingApp extends JFrame {
    
    // Classes internes pour éviter les dépendances
    public static class Point {
        private final double x, y;
        
        public Point(double x, double y) {
            this.x = x;
            this.y = y;
        }
        
        public double getX() { return x; }
        public double getY() { return y; }
        
        public double distanceTo(Point other) {
            double dx = this.x - other.x;
            double dy = this.y - other.y;
            return Math.sqrt(dx * dx + dy * dy);
        }
        
        @Override
        public String toString() {
            return String.format("(%.0f,%.0f)", x, y);
        }
    }
    
    // Factory Pattern pour les formes
    public enum ShapeType {
        RECTANGLE("📐 Rectangle"),
        CIRCLE("⭕ Cercle"),
        LINE("📏 Ligne");
        
        private final String displayName;
        
        ShapeType(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
    
    // Classe pour stocker une forme dessinée
    private static class DrawnShape {
        Point start, end;
        ShapeType type;
        Color color;
        
        DrawnShape(Point start, Point end, ShapeType type, Color color) {
            this.start = start;
            this.end = end;
            this.type = type;
            this.color = color;
        }
    }
    
    // Strategy Pattern pour le logging
    public interface LogStrategy {
        void log(String message);
    }
    
    public static class ConsoleLogStrategy implements LogStrategy {
        @Override
        public void log(String message) {
            System.out.println("📝 " + message);
        }
    }
    
    public static class StatusLogStrategy implements LogStrategy {
        private JLabel statusLabel;
        
        public StatusLogStrategy(JLabel statusLabel) {
            this.statusLabel = statusLabel;
        }
        
        @Override
        public void log(String message) {
            statusLabel.setText("🎯 " + message);
            System.out.println("✅ " + message);
        }
    }
    
    // Command Pattern pour les actions
    public interface Command {
        void execute();
        void undo();
        String getDescription();
    }
    
    public class AddShapeCommand implements Command {
        private DrawnShape shape;
        private boolean executed = false;
        
        public AddShapeCommand(DrawnShape shape) {
            this.shape = shape;
        }
        
        @Override
        public void execute() {
            if (!executed) {
                shapes.add(shape);
                executed = true;
                drawingPanel.repaint();
                logger.log("Forme ajoutée: " + shape.type.getDisplayName());
            }
        }
        
        @Override
        public void undo() {
            if (executed) {
                shapes.remove(shape);
                executed = false;
                drawingPanel.repaint();
                logger.log("Forme supprimée: " + shape.type.getDisplayName());
            }
        }
        
        @Override
        public String getDescription() {
            return "Ajouter " + shape.type.getDisplayName();
        }
    }
    
    public class ClearCommand implements Command {
        private List<DrawnShape> savedShapes;
        private boolean executed = false;
        
        @Override
        public void execute() {
            if (!executed) {
                savedShapes = new ArrayList<>(shapes);
                shapes.clear();
                executed = true;
                drawingPanel.repaint();
                logger.log("Canvas effacé (" + savedShapes.size() + " formes supprimées)");
            }
        }
        
        @Override
        public void undo() {
            if (executed && savedShapes != null) {
                shapes.addAll(savedShapes);
                executed = false;
                drawingPanel.repaint();
                logger.log("Canvas restauré (" + savedShapes.size() + " formes restaurées)");
            }
        }
        
        @Override
        public String getDescription() {
            return "Effacer tout";
        }
    }
    
    // Gestionnaire de commandes
    public class CommandManager {
        private List<Command> history = new ArrayList<>();
        private int currentIndex = -1;
        
        public void executeCommand(Command command) {
            // Supprimer les commandes après l'index actuel
            if (currentIndex < history.size() - 1) {
                history.subList(currentIndex + 1, history.size()).clear();
            }
            
            command.execute();
            history.add(command);
            currentIndex++;
            
            updateUndoRedoButtons();
        }
        
        public boolean undo() {
            if (canUndo()) {
                Command command = history.get(currentIndex);
                command.undo();
                currentIndex--;
                updateUndoRedoButtons();
                return true;
            }
            return false;
        }
        
        public boolean redo() {
            if (canRedo()) {
                currentIndex++;
                Command command = history.get(currentIndex);
                command.execute();
                updateUndoRedoButtons();
                return true;
            }
            return false;
        }
        
        public boolean canUndo() {
            return currentIndex >= 0;
        }
        
        public boolean canRedo() {
            return currentIndex + 1 < history.size();
        }
    }
    
    // Variables d'instance
    private DrawingPanel drawingPanel;
    private ShapeType currentShapeType = ShapeType.RECTANGLE;
    private Color currentColor = Color.BLACK;
    private JLabel statusLabel;
    private LogStrategy logger;
    private CommandManager commandManager;
    private JButton undoButton, redoButton;
    
    // Liste des formes dessinées
    private List<DrawnShape> shapes = new ArrayList<>();
    
    public StandaloneDrawingApp() {
        setTitle("🎨 Application de Dessin Géométrique - Design Patterns (Swing)");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1000, 700);
        setLocationRelativeTo(null);
        
        commandManager = new CommandManager();
        initializeUI();
        
        System.out.println("🎨 === APPLICATION DE DESSIN DÉMARRÉE ===");
        System.out.println("✅ Design Patterns implémentés:");
        System.out.println("   • Factory Pattern - Création des formes");
        System.out.println("   • Strategy Pattern - Logging et types de formes");
        System.out.println("   • Command Pattern - Actions avec undo/redo");
        System.out.println("   • MVC Pattern - Séparation des responsabilités");
        logger.log("Application initialisée avec succès");
    }
    
    private void initializeUI() {
        setLayout(new BorderLayout());
        
        // Barre d'outils en haut
        add(createToolBar(), BorderLayout.NORTH);
        
        // Zone de dessin au centre
        drawingPanel = new DrawingPanel();
        add(new JScrollPane(drawingPanel), BorderLayout.CENTER);
        
        // Barre de statut en bas
        statusLabel = new JLabel("🎯 Prêt - Sélectionnez une forme et dessinez!");
        statusLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
        statusLabel.setBackground(Color.LIGHT_GRAY);
        statusLabel.setOpaque(true);
        add(statusLabel, BorderLayout.SOUTH);
        
        // Initialiser le logger avec la stratégie de statut
        logger = new StatusLogStrategy(statusLabel);
    }
    
    private JPanel createToolBar() {
        JPanel toolBar = new JPanel(new FlowLayout(FlowLayout.LEFT));
        toolBar.setBorder(BorderFactory.createEtchedBorder());
        
        // Titre
        JLabel titleLabel = new JLabel("🎨 Formes:");
        titleLabel.setFont(titleLabel.getFont().deriveFont(Font.BOLD));
        toolBar.add(titleLabel);
        
        toolBar.add(new JSeparator(SwingConstants.VERTICAL));
        
        // Boutons de sélection de formes (Factory Pattern)
        ButtonGroup shapeGroup = new ButtonGroup();
        
        for (ShapeType type : ShapeType.values()) {
            JRadioButton button = new JRadioButton(type.getDisplayName(), type == ShapeType.RECTANGLE);
            button.addActionListener(e -> {
                currentShapeType = type;
                logger.log(type.getDisplayName() + " sélectionné");
            });
            shapeGroup.add(button);
            toolBar.add(button);
        }
        
        toolBar.add(new JSeparator(SwingConstants.VERTICAL));
        
        // Sélecteur de couleur
        JLabel colorLabel = new JLabel("🎨 Couleur:");
        toolBar.add(colorLabel);
        
        JButton colorButton = new JButton();
        colorButton.setBackground(Color.BLACK);
        colorButton.setPreferredSize(new Dimension(30, 25));
        colorButton.addActionListener(e -> {
            Color newColor = JColorChooser.showDialog(this, "Choisir une couleur", currentColor);
            if (newColor != null) {
                currentColor = newColor;
                colorButton.setBackground(currentColor);
                logger.log("Couleur changée: " + getColorName(currentColor));
            }
        });
        toolBar.add(colorButton);
        
        toolBar.add(new JSeparator(SwingConstants.VERTICAL));
        
        // Boutons d'action (Command Pattern)
        undoButton = new JButton("↶ Annuler");
        undoButton.addActionListener(e -> {
            if (commandManager.undo()) {
                logger.log("Action annulée");
            }
        });
        toolBar.add(undoButton);
        
        redoButton = new JButton("↷ Rétablir");
        redoButton.addActionListener(e -> {
            if (commandManager.redo()) {
                logger.log("Action rétablie");
            }
        });
        toolBar.add(redoButton);
        
        JButton clearButton = new JButton("🗑 Effacer");
        clearButton.addActionListener(e -> {
            commandManager.executeCommand(new ClearCommand());
        });
        toolBar.add(clearButton);
        
        // Bouton démo
        JButton demoButton = new JButton("🚀 Démo");
        demoButton.addActionListener(e -> runDemo());
        toolBar.add(demoButton);
        
        updateUndoRedoButtons();
        return toolBar;
    }
    
    private void updateUndoRedoButtons() {
        undoButton.setEnabled(commandManager.canUndo());
        redoButton.setEnabled(commandManager.canRedo());
    }
    
    private String getColorName(Color color) {
        if (color.equals(Color.BLACK)) return "Noir";
        if (color.equals(Color.RED)) return "Rouge";
        if (color.equals(Color.BLUE)) return "Bleu";
        if (color.equals(Color.GREEN)) return "Vert";
        if (color.equals(Color.YELLOW)) return "Jaune";
        return "Personnalisée";
    }
    
    private void runDemo() {
        System.out.println("🚀 === DÉMONSTRATION AUTOMATIQUE ===");
        
        // Effacer d'abord
        commandManager.executeCommand(new ClearCommand());
        
        // Créer des formes de démonstration
        DrawnShape[] demoShapes = {
            new DrawnShape(new Point(50, 80), new Point(200, 180), ShapeType.RECTANGLE, Color.RED),
            new DrawnShape(new Point(250, 130), new Point(350, 180), ShapeType.CIRCLE, Color.BLUE),
            new DrawnShape(new Point(400, 80), new Point(550, 180), ShapeType.LINE, Color.GREEN),
            new DrawnShape(new Point(50, 250), new Point(150, 330), ShapeType.RECTANGLE, Color.MAGENTA),
            new DrawnShape(new Point(200, 280), new Point(280, 320), ShapeType.CIRCLE, Color.ORANGE)
        };
        
        for (DrawnShape shape : demoShapes) {
            commandManager.executeCommand(new AddShapeCommand(shape));
        }
        
        logger.log("Démonstration terminée - " + demoShapes.length + " formes créées");
        System.out.println("🎉 Démonstration terminée!");
    }
    
    // Panel de dessin personnalisé
    private class DrawingPanel extends JPanel {
        private Point startPoint;
        private Point currentPoint;
        private boolean isDrawing = false;
        
        public DrawingPanel() {
            setBackground(Color.WHITE);
            setPreferredSize(new Dimension(800, 500));
            
            MouseAdapter mouseHandler = new MouseAdapter() {
                @Override
                public void mousePressed(MouseEvent e) {
                    startPoint = new Point(e.getX(), e.getY());
                    isDrawing = true;
                    logger.log("Début du dessin à: " + startPoint);
                }
                
                @Override
                public void mouseDragged(MouseEvent e) {
                    if (isDrawing) {
                        currentPoint = new Point(e.getX(), e.getY());
                        repaint();
                    }
                }
                
                @Override
                public void mouseReleased(MouseEvent e) {
                    if (isDrawing && startPoint != null) {
                        Point endPoint = new Point(e.getX(), e.getY());
                        DrawnShape newShape = new DrawnShape(startPoint, endPoint, currentShapeType, currentColor);
                        commandManager.executeCommand(new AddShapeCommand(newShape));
                        
                        logger.log("Forme créée: " + currentShapeType.getDisplayName() + 
                                 " de " + startPoint + " à " + endPoint);
                    }
                    isDrawing = false;
                    startPoint = null;
                    currentPoint = null;
                }
                
                @Override
                public void mouseMoved(MouseEvent e) {
                    statusLabel.setText("🎯 Position: (" + e.getX() + ", " + e.getY() + ")");
                }
            };
            
            addMouseListener(mouseHandler);
            addMouseMotionListener(mouseHandler);
        }
        
        @Override
        protected void paintComponent(Graphics g) {
            super.paintComponent(g);
            Graphics2D g2d = (Graphics2D) g;
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            
            // Dessiner toutes les formes sauvegardées
            for (DrawnShape shape : shapes) {
                drawShape(g2d, shape.start, shape.end, shape.type, shape.color, false);
            }
            
            // Dessiner la prévisualisation si en cours de dessin
            if (isDrawing && startPoint != null && currentPoint != null) {
                drawShape(g2d, startPoint, currentPoint, currentShapeType, currentColor, true);
            }
            
            // Message d'accueil si aucune forme
            if (shapes.isEmpty() && !isDrawing) {
                g2d.setColor(Color.GRAY);
                g2d.drawString("🎯 Cliquez et glissez pour dessiner des formes!", 20, 30);
                g2d.drawString("✨ Utilisez la barre d'outils pour changer de forme et de couleur", 20, 50);
                g2d.drawString("🔄 Testez les boutons Annuler/Rétablir et la démonstration", 20, 70);
            }
        }
        
        private void drawShape(Graphics2D g2d, Point start, Point end, 
                              ShapeType type, Color color, boolean preview) {
            g2d.setColor(color);
            g2d.setStroke(new BasicStroke(preview ? 1 : 2, BasicStroke.CAP_ROUND, BasicStroke.JOIN_ROUND));
            
            if (preview) {
                g2d.setStroke(new BasicStroke(1, BasicStroke.CAP_ROUND, BasicStroke.JOIN_ROUND, 
                                            0, new float[]{5}, 0));
            }
            
            switch (type) {
                case RECTANGLE:
                    int width = (int) Math.abs(end.getX() - start.getX());
                    int height = (int) Math.abs(end.getY() - start.getY());
                    int x = (int) Math.min(start.getX(), end.getX());
                    int y = (int) Math.min(start.getY(), end.getY());
                    g2d.drawRect(x, y, width, height);
                    break;
                    
                case CIRCLE:
                    double radius = start.distanceTo(end);
                    int diameter = (int) (radius * 2);
                    g2d.drawOval((int)(start.getX() - radius), (int)(start.getY() - radius), 
                               diameter, diameter);
                    break;
                    
                case LINE:
                    g2d.drawLine((int)start.getX(), (int)start.getY(), 
                               (int)end.getX(), (int)end.getY());
                    break;
            }
        }
    }
    
    public static void main(String[] args) {
        System.out.println("🚀 Lancement de l'application Swing autonome...");
        
        // Utiliser le look and feel par défaut
        
        SwingUtilities.invokeLater(() -> {
            new StandaloneDrawingApp().setVisible(true);
        });
    }
}
