package com.drawing.patterns;

import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * Démonstration simplifiée du Strategy Pattern pour la journalisation
 * Sans dépendances externes
 */
public class LoggingStrategySimple {
    
    /**
     * Interface Strategy pour la journalisation
     */
    public interface LogStrategy {
        void log(String message);
        void log(LogLevel level, String message);
        String getStrategyName();
        void close();
    }
    
    /**
     * Niveaux de log
     */
    public enum LogLevel {
        DEBUG("DEBUG"),
        INFO("INFO"),
        WARN("WARN"),
        ERROR("ERROR");
        
        private final String name;
        
        LogLevel(String name) {
            this.name = name;
        }
        
        public String getName() {
            return name;
        }
    }
    
    /**
     * Stratégie de logging vers la console
     */
    public static class ConsoleLogStrategy implements LogStrategy {
        private static final DateTimeFormatter TIMESTAMP_FORMAT = 
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        
        @Override
        public void log(String message) {
            log(LogLevel.INFO, message);
        }
        
        @Override
        public void log(LogLevel level, String message) {
            String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
            String formattedMessage = String.format("[%s] [%s] %s", timestamp, level.getName(), message);
            System.out.println(formattedMessage);
        }
        
        @Override
        public String getStrategyName() {
            return "Console";
        }
        
        @Override
        public void close() {
            log(LogLevel.INFO, "Console logger fermé");
        }
    }
    
    /**
     * Stratégie de logging vers un fichier
     */
    public static class FileLogStrategy implements LogStrategy {
        private static final DateTimeFormatter TIMESTAMP_FORMAT = 
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        
        private final String filename;
        private FileWriter writer;
        private boolean isAvailable;
        
        public FileLogStrategy(String filename) {
            this.filename = filename;
            this.isAvailable = initializeWriter();
        }
        
        private boolean initializeWriter() {
            try {
                this.writer = new FileWriter(filename, true); // Mode append
                log(LogLevel.INFO, "=== Session de logging démarrée ===");
                return true;
            } catch (IOException e) {
                System.err.println("Erreur lors de l'ouverture du fichier de log: " + e.getMessage());
                return false;
            }
        }
        
        @Override
        public void log(String message) {
            log(LogLevel.INFO, message);
        }
        
        @Override
        public void log(LogLevel level, String message) {
            if (!isAvailable) return;
            
            try {
                String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
                String formattedMessage = String.format("[%s] [%s] %s%n", timestamp, level.getName(), message);
                writer.write(formattedMessage);
                writer.flush();
            } catch (IOException e) {
                System.err.println("Erreur lors de l'écriture dans le fichier: " + e.getMessage());
                isAvailable = false;
            }
        }
        
        @Override
        public String getStrategyName() {
            return "Fichier (" + filename + ")";
        }
        
        @Override
        public void close() {
            if (writer != null) {
                try {
                    log(LogLevel.INFO, "=== Session de logging terminée ===");
                    writer.close();
                } catch (IOException e) {
                    System.err.println("Erreur lors de la fermeture du fichier: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * Stratégie de logging en mémoire (pour tests)
     */
    public static class MemoryLogStrategy implements LogStrategy {
        private final List<String> logs = new ArrayList<>();
        private static final DateTimeFormatter TIMESTAMP_FORMAT = 
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        
        @Override
        public void log(String message) {
            log(LogLevel.INFO, message);
        }
        
        @Override
        public void log(LogLevel level, String message) {
            String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
            String formattedMessage = String.format("[%s] [%s] %s", timestamp, level.getName(), message);
            logs.add(formattedMessage);
        }
        
        @Override
        public String getStrategyName() {
            return "Mémoire";
        }
        
        @Override
        public void close() {
            log(LogLevel.INFO, "Memory logger fermé");
        }
        
        public List<String> getLogs() {
            return new ArrayList<>(logs);
        }
        
        public void printLogs() {
            System.out.println("=== Logs en mémoire ===");
            for (String log : logs) {
                System.out.println(log);
            }
        }
        
        public void clearLogs() {
            logs.clear();
        }
    }
    
    /**
     * Context qui utilise les stratégies
     */
    public static class Logger {
        private LogStrategy strategy;
        
        public Logger(LogStrategy strategy) {
            this.strategy = strategy;
        }
        
        public void setStrategy(LogStrategy strategy) {
            if (this.strategy != null) {
                log(LogLevel.INFO, "Changement de stratégie vers: " + strategy.getStrategyName());
            }
            this.strategy = strategy;
            log(LogLevel.INFO, "Stratégie active: " + strategy.getStrategyName());
        }
        
        public void log(String message) {
            if (strategy != null) {
                strategy.log(message);
            }
        }
        
        public void log(LogLevel level, String message) {
            if (strategy != null) {
                strategy.log(level, message);
            }
        }
        
        public void logUserAction(String action, String details) {
            log(LogLevel.INFO, String.format("Action utilisateur: %s - %s", action, details));
        }
        
        public void logError(String message) {
            log(LogLevel.ERROR, message);
        }
        
        public void logWarning(String message) {
            log(LogLevel.WARN, message);
        }
        
        public void logDebug(String message) {
            log(LogLevel.DEBUG, message);
        }
        
        public String getCurrentStrategyName() {
            return strategy != null ? strategy.getStrategyName() : "Aucune";
        }
        
        public void close() {
            if (strategy != null) {
                strategy.close();
            }
        }
    }
    
    /**
     * Démonstration du Strategy Pattern
     */
    public static void main(String[] args) {
        System.out.println("=== Démonstration du Strategy Pattern pour Logging ===");
        
        // Créer les différentes stratégies
        LogStrategy consoleStrategy = new ConsoleLogStrategy();
        LogStrategy fileStrategy = new FileLogStrategy("logs/demo.log");
        LogStrategy memoryStrategy = new MemoryLogStrategy();
        
        // Créer le logger avec la stratégie console
        Logger logger = new Logger(consoleStrategy);
        
        System.out.println("\n1. Test avec stratégie Console:");
        logger.log("Message de test");
        logger.logUserAction("Sélection", "Rectangle choisi");
        logger.logWarning("Ceci est un avertissement");
        logger.logError("Ceci est une erreur");
        
        System.out.println("\n2. Changement vers stratégie Fichier:");
        logger.setStrategy(fileStrategy);
        logger.log("Message dans le fichier");
        logger.logUserAction("Dessin", "Rectangle créé aux coordonnées (10,20)");
        
        System.out.println("\n3. Changement vers stratégie Mémoire:");
        logger.setStrategy(memoryStrategy);
        logger.log("Message en mémoire");
        logger.logUserAction("Sauvegarde", "Dessin sauvegardé");
        logger.logDebug("Information de debug");
        
        // Afficher les logs en mémoire
        System.out.println("\n4. Affichage des logs en mémoire:");
        ((MemoryLogStrategy) memoryStrategy).printLogs();
        
        System.out.println("\n5. Test de changement dynamique de stratégie:");
        logger.setStrategy(consoleStrategy);
        logger.log("Retour à la console");
        
        logger.setStrategy(memoryStrategy);
        logger.log("Retour à la mémoire");
        
        // Fermer les ressources
        logger.close();
        fileStrategy.close();
        
        System.out.println("\n✅ Démonstration du Strategy Pattern terminée!");
        System.out.println("Vérifiez le fichier 'logs/demo.log' pour voir les messages de fichier.");
    }
}
