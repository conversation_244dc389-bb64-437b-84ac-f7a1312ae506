package com.drawing.model;

/**
 * Représente les limites rectangulaires d'une forme (bounding box)
 */
public class Bounds {
    private final double minX;
    private final double minY;
    private final double maxX;
    private final double maxY;

    public Bounds(double minX, double minY, double maxX, double maxY) {
        this.minX = minX;
        this.minY = minY;
        this.maxX = maxX;
        this.maxY = maxY;
    }

    public double getMinX() {
        return minX;
    }

    public double getMinY() {
        return minY;
    }

    public double getMaxX() {
        return maxX;
    }

    public double getMaxY() {
        return maxY;
    }

    public double getWidth() {
        return maxX - minX;
    }

    public double getHeight() {
        return maxY - minY;
    }

    /**
     * Vérifie si un point est contenu dans ces limites
     */
    public boolean contains(Point point) {
        return point.getX() >= minX && point.getX() <= maxX &&
               point.getY() >= minY && point.getY() <= maxY;
    }

    /**
     * Vérifie si ces limites intersectent avec d'autres limites
     */
    public boolean intersects(Bounds other) {
        return !(other.maxX < minX || other.minX > maxX ||
                 other.maxY < minY || other.minY > maxY);
    }

    @Override
    public String toString() {
        return String.format("Bounds[%.2f, %.2f, %.2f, %.2f]", minX, minY, maxX, maxY);
    }
}
