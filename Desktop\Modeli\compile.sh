#!/bin/bash

echo "=== Compilation de l'application de dessin géométrique ==="

# Vérifier si Java est installé
if ! command -v java &> /dev/null; then
    echo "Erreur: Java n'est pas installé ou n'est pas dans le PATH"
    exit 1
fi

# Vérifier la version de Java
java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
echo "Version Java détectée: $java_version"

# Créer les répertoires nécessaires
mkdir -p target/classes
mkdir -p data
mkdir -p logs

echo "Compilation des sources Java (version simplifiée)..."

# Compiler les classes de base sans dépendances externes
javac -d target/classes \
      -cp "." \
      src/main/java/com/drawing/model/Point.java \
      src/main/java/com/drawing/model/Bounds.java

if [ $? -ne 0 ]; then
    echo "Erreur lors de la compilation des classes de base"
    exit 1
fi

echo "Compilation des classes de base terminée avec succès!"

# Essayer de compiler les autres classes
echo "Tentative de compilation des autres classes..."

javac -d target/classes \
      -cp "target/classes" \
      src/main/java/com/drawing/patterns/ShapeFactory.java

if [ $? -eq 0 ]; then
    echo "ShapeFactory compilé avec succès!"
else
    echo "Erreur lors de la compilation de ShapeFactory (dépendances manquantes)"
fi

echo ""
echo "Note: Pour une compilation complète, installez Maven et les dépendances JavaFX"
echo "Puis utilisez: mvn clean compile"
