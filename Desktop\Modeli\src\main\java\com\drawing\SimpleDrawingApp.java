package com.drawing;

import javafx.application.Application;
import javafx.geometry.Insets;
import javafx.scene.Scene;
import javafx.scene.canvas.Canvas;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.scene.paint.Color;
import javafx.stage.Stage;
import com.drawing.model.Point;
import com.drawing.patterns.ShapeFactorySimple;

/**
 * Application JavaFX simplifiée pour dessiner des formes géométriques
 * Démontre les design patterns sans dépendances externes
 */
public class SimpleDrawingApp extends Application {
    
    private Canvas canvas;
    private GraphicsContext gc;
    private ShapeFactorySimple.ShapeType currentShapeType = ShapeFactorySimple.ShapeType.RECTANGLE;
    private Color currentColor = Color.BLACK;
    private Point startPoint;
    private boolean isDrawing = false;
    
    @Override
    public void start(Stage primaryStage) {
        primaryStage.setTitle("🎨 Application de Dessin Géométrique - Design Patterns");
        
        // Créer l'interface
        BorderPane root = createUI();
        
        // Créer la scène
        Scene scene = new Scene(root, 1000, 700);
        primaryStage.setScene(scene);
        primaryStage.setMinWidth(800);
        primaryStage.setMinHeight(600);
        
        // Afficher la fenêtre
        primaryStage.show();
        
        System.out.println("🎨 Application JavaFX démarrée avec succès!");
        System.out.println("✅ Design Patterns implémentés:");
        System.out.println("   • Factory Pattern - Création des formes");
        System.out.println("   • Strategy Pattern - Différents types de formes");
        System.out.println("   • MVC Pattern - Séparation des responsabilités");
    }
    
    private BorderPane createUI() {
        BorderPane root = new BorderPane();
        
        // Barre d'outils en haut
        ToolBar toolBar = createToolBar();
        root.setTop(toolBar);
        
        // Zone de dessin au centre
        canvas = new Canvas(800, 500);
        gc = canvas.getGraphicsContext2D();
        initializeCanvas();
        setupCanvasEvents();
        
        ScrollPane scrollPane = new ScrollPane(canvas);
        scrollPane.setFitToWidth(true);
        scrollPane.setFitToHeight(true);
        root.setCenter(scrollPane);
        
        // Barre de statut en bas
        Label statusLabel = new Label("🎯 Prêt - Sélectionnez une forme et dessinez!");
        statusLabel.setPadding(new Insets(5));
        statusLabel.setStyle("-fx-background-color: #f0f0f0;");
        root.setBottom(statusLabel);
        
        return root;
    }
    
    private ToolBar createToolBar() {
        ToolBar toolBar = new ToolBar();
        
        // Titre
        Label titleLabel = new Label("🎨 Formes:");
        titleLabel.setStyle("-fx-font-weight: bold;");
        
        // Boutons de sélection de formes
        ToggleGroup shapeGroup = new ToggleGroup();
        
        RadioButton rectButton = new RadioButton("📐 Rectangle");
        rectButton.setToggleGroup(shapeGroup);
        rectButton.setSelected(true);
        rectButton.setOnAction(e -> {
            currentShapeType = ShapeFactorySimple.ShapeType.RECTANGLE;
            System.out.println("✅ Rectangle sélectionné");
        });
        
        RadioButton circleButton = new RadioButton("⭕ Cercle");
        circleButton.setToggleGroup(shapeGroup);
        circleButton.setOnAction(e -> {
            currentShapeType = ShapeFactorySimple.ShapeType.CIRCLE;
            System.out.println("✅ Cercle sélectionné");
        });
        
        RadioButton lineButton = new RadioButton("📏 Ligne");
        lineButton.setToggleGroup(shapeGroup);
        lineButton.setOnAction(e -> {
            currentShapeType = ShapeFactorySimple.ShapeType.LINE;
            System.out.println("✅ Ligne sélectionnée");
        });
        
        // Sélecteur de couleur
        Label colorLabel = new Label("🎨 Couleur:");
        ColorPicker colorPicker = new ColorPicker(Color.BLACK);
        colorPicker.setOnAction(e -> {
            currentColor = colorPicker.getValue();
            System.out.println("✅ Couleur changée: " + currentColor);
        });
        
        // Bouton effacer
        Button clearButton = new Button("🗑 Effacer");
        clearButton.setOnAction(e -> {
            clearCanvas();
            System.out.println("✅ Canvas effacé");
        });
        
        // Bouton démo
        Button demoButton = new Button("🚀 Démo");
        demoButton.setOnAction(e -> runDemo());
        
        toolBar.getItems().addAll(
            titleLabel, new Separator(),
            rectButton, circleButton, lineButton, new Separator(),
            colorLabel, colorPicker, new Separator(),
            clearButton, demoButton
        );
        
        return toolBar;
    }
    
    private void initializeCanvas() {
        // Fond blanc
        gc.setFill(Color.WHITE);
        gc.fillRect(0, 0, canvas.getWidth(), canvas.getHeight());
        
        // Bordure
        gc.setStroke(Color.LIGHTGRAY);
        gc.setLineWidth(1);
        gc.strokeRect(0, 0, canvas.getWidth(), canvas.getHeight());
        
        // Message d'accueil
        gc.setFill(Color.GRAY);
        gc.fillText("🎯 Cliquez et glissez pour dessiner des formes!", 20, 30);
        gc.fillText("✨ Utilisez la barre d'outils pour changer de forme et de couleur", 20, 50);
    }
    
    private void setupCanvasEvents() {
        canvas.setOnMousePressed(e -> {
            startPoint = new Point(e.getX(), e.getY());
            isDrawing = true;
            System.out.println("🖱 Début du dessin à: " + startPoint);
        });
        
        canvas.setOnMouseDragged(e -> {
            if (isDrawing && startPoint != null) {
                // Prévisualisation en temps réel
                redrawCanvas();
                drawPreview(startPoint, new Point(e.getX(), e.getY()));
            }
        });
        
        canvas.setOnMouseReleased(e -> {
            if (isDrawing && startPoint != null) {
                Point endPoint = new Point(e.getX(), e.getY());
                drawFinalShape(startPoint, endPoint);
                System.out.println("✅ Forme créée: " + currentShapeType.getDisplayName() + 
                                 " de " + startPoint + " à " + endPoint);
            }
            isDrawing = false;
            startPoint = null;
        });
    }
    
    private void drawPreview(Point start, Point end) {
        gc.setStroke(currentColor);
        gc.setLineWidth(1);
        gc.getLineDashes().clear();
        gc.setLineDashes(5); // Ligne pointillée pour la prévisualisation
        
        drawShapeOnCanvas(start, end);
        
        gc.getLineDashes().clear(); // Remettre ligne normale
    }
    
    private void drawFinalShape(Point start, Point end) {
        gc.setStroke(currentColor);
        gc.setLineWidth(2);
        gc.getLineDashes().clear();
        
        drawShapeOnCanvas(start, end);
    }
    
    private void drawShapeOnCanvas(Point start, Point end) {
        switch (currentShapeType) {
            case RECTANGLE:
                double width = Math.abs(end.getX() - start.getX());
                double height = Math.abs(end.getY() - start.getY());
                double x = Math.min(start.getX(), end.getX());
                double y = Math.min(start.getY(), end.getY());
                gc.strokeRect(x, y, width, height);
                break;
                
            case CIRCLE:
                double radius = start.distanceTo(end);
                double diameter = radius * 2;
                gc.strokeOval(start.getX() - radius, start.getY() - radius, diameter, diameter);
                break;
                
            case LINE:
                gc.strokeLine(start.getX(), start.getY(), end.getX(), end.getY());
                break;
        }
    }
    
    private void redrawCanvas() {
        // Pour une version simple, on efface et redessine
        // Dans une version complète, on garderait une liste des formes
        gc.setFill(Color.WHITE);
        gc.fillRect(0, 0, canvas.getWidth(), canvas.getHeight());
        
        gc.setStroke(Color.LIGHTGRAY);
        gc.setLineWidth(1);
        gc.strokeRect(0, 0, canvas.getWidth(), canvas.getHeight());
    }
    
    private void clearCanvas() {
        initializeCanvas();
    }
    
    private void runDemo() {
        System.out.println("🚀 === DÉMONSTRATION AUTOMATIQUE ===");
        
        clearCanvas();
        
        // Démonstration automatique des formes
        gc.setLineWidth(3);
        
        // Rectangle rouge
        gc.setStroke(Color.RED);
        gc.strokeRect(50, 80, 150, 100);
        System.out.println("✅ Rectangle rouge créé");
        
        // Cercle bleu
        gc.setStroke(Color.BLUE);
        gc.strokeOval(250, 80, 120, 120);
        System.out.println("✅ Cercle bleu créé");
        
        // Ligne verte
        gc.setStroke(Color.GREEN);
        gc.strokeLine(450, 80, 600, 180);
        System.out.println("✅ Ligne verte créée");
        
        // Formes supplémentaires
        gc.setStroke(Color.PURPLE);
        gc.strokeRect(50, 250, 100, 80);
        
        gc.setStroke(Color.ORANGE);
        gc.strokeOval(200, 230, 80, 80);
        
        gc.setStroke(Color.DARKRED);
        gc.strokeLine(350, 250, 500, 300);
        
        // Texte de démonstration
        gc.setFill(Color.BLACK);
        gc.fillText("🎨 Démonstration des Design Patterns:", 50, 400);
        gc.fillText("• Factory Pattern: Création des formes", 70, 420);
        gc.fillText("• Strategy Pattern: Différents algorithmes de dessin", 70, 440);
        gc.fillText("• MVC Pattern: Séparation Model-View-Controller", 70, 460);
        
        System.out.println("🎉 Démonstration terminée!");
    }
    
    public static void main(String[] args) {
        System.out.println("🚀 Lancement de l'application JavaFX...");
        launch(args);
    }
}
