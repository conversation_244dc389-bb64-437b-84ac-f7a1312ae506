package com.drawing.model;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests unitaires pour la classe Point
 * Démontre l'utilisation des tests avec JUnit 5
 */
public class PointTest {
    
    private Point origin;
    private Point point1;
    private Point point2;
    
    @BeforeEach
    void setUp() {
        origin = new Point(0, 0);
        point1 = new Point(3, 4);
        point2 = new Point(-2, 1);
    }
    
    @Test
    void testConstructor() {
        Point p = new Point(5.5, -3.2);
        assertEquals(5.5, p.getX(), 0.001);
        assertEquals(-3.2, p.getY(), 0.001);
    }
    
    @Test
    void testGetters() {
        assertEquals(0, origin.getX());
        assertEquals(0, origin.getY());
        
        assertEquals(3, point1.getX());
        assertEquals(4, point1.getY());
    }
    
    @Test
    void testDistanceTo() {
        // Distance de l'origine au point (3,4) doit être 5
        assertEquals(5.0, origin.distanceTo(point1), 0.001);
        
        // Distance d'un point à lui-même doit être 0
        assertEquals(0.0, point1.distanceTo(point1), 0.001);
        
        // Distance symétrique
        assertEquals(point1.distanceTo(point2), point2.distanceTo(point1), 0.001);
    }
    
    @Test
    void testEquals() {
        Point sameAsOrigin = new Point(0, 0);
        Point different = new Point(1, 1);
        
        // Test d'égalité
        assertEquals(origin, sameAsOrigin);
        assertNotEquals(origin, different);
        assertNotEquals(origin, null);
        assertNotEquals(origin, "not a point");
        
        // Réflexivité
        assertEquals(origin, origin);
    }
    
    @Test
    void testHashCode() {
        Point sameAsOrigin = new Point(0, 0);
        
        // Objets égaux doivent avoir le même hashCode
        assertEquals(origin.hashCode(), sameAsOrigin.hashCode());
    }
    
    @Test
    void testToString() {
        String str = origin.toString();
        assertNotNull(str);
        assertTrue(str.contains("0"));
        assertTrue(str.contains("Point"));
    }
    
    @Test
    void testDistanceCalculation() {
        // Test avec des valeurs connues
        Point p1 = new Point(0, 0);
        Point p2 = new Point(3, 4);
        Point p3 = new Point(6, 8);
        
        // Triangle 3-4-5
        assertEquals(5.0, p1.distanceTo(p2), 0.001);
        
        // Distance doublée
        assertEquals(10.0, p1.distanceTo(p3), 0.001);
    }
    
    @Test
    void testNegativeCoordinates() {
        Point negative = new Point(-5, -12);
        assertEquals(13.0, origin.distanceTo(negative), 0.001);
    }
    
    @Test
    void testDecimalCoordinates() {
        Point decimal1 = new Point(1.5, 2.5);
        Point decimal2 = new Point(4.5, 6.5);
        
        double expectedDistance = Math.sqrt(9 + 16); // sqrt((4.5-1.5)² + (6.5-2.5)²)
        assertEquals(expectedDistance, decimal1.distanceTo(decimal2), 0.001);
    }
}
