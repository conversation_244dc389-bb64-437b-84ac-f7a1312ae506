package com.drawing.model;

import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Implémentation d'un cercle
 */
public class Circle implements Shape {
    private final Point center;
    private final double radius;
    private Color color;

    @JsonCreator
    public Circle(@JsonProperty("center") Point center, 
                  @JsonProperty("radius") double radius,
                  @JsonProperty("color") Color color) {
        this.center = center;
        this.radius = Math.abs(radius);
        this.color = color != null ? color : Color.BLACK;
    }

    public Circle(Point center, double radius) {
        this(center, radius, Color.BLACK);
    }

    @Override
    public void draw(GraphicsContext gc) {
        gc.setStroke(color);
        gc.setLineWidth(2.0);
        double diameter = radius * 2;
        gc.strokeOval(center.getX() - radius, center.getY() - radius, diameter, diameter);
    }

    @Override
    public String getType() {
        return "circle";
    }

    @Override
    public Color getColor() {
        return color;
    }

    @Override
    public void setColor(Color color) {
        this.color = color;
    }

    @Override
    public boolean contains(Point point) {
        return center.distanceTo(point) <= radius;
    }

    @Override
    public Bounds getBounds() {
        return new Bounds(center.getX() - radius, center.getY() - radius,
                         center.getX() + radius, center.getY() + radius);
    }

    @Override
    public Shape clone() {
        return new Circle(center, radius, color);
    }

    @Override
    public String toJson() {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            return "{}";
        }
    }

    // Getters
    public Point getCenter() {
        return center;
    }

    public double getRadius() {
        return radius;
    }

    @Override
    public String toString() {
        return String.format("Circle[center=%s, radius=%.2f, color=%s]", 
                           center, radius, color);
    }
}
