@echo off
echo === Execution de l'application de dessin geometrique ===

REM Verifier si Java est installe
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Erreur: Java n'est pas installe ou n'est pas dans le PATH
    pause
    exit /b 1
)

REM Verifier si les classes sont compilees
if not exist "target\classes\com\drawing\DrawingApp.class" (
    echo Erreur: L'application n'est pas compilee
    echo Executez d'abord compile.bat
    pause
    exit /b 1
)

echo Demarrage de l'application...

REM Executer l'application avec JavaFX
java --module-path "C:\Program Files\Java\javafx-sdk-19\lib" ^
     --add-modules javafx.controls,javafx.fxml ^
     -cp "target\classes;lib\*" ^
     com.drawing.DrawingApp

if %errorlevel% neq 0 (
    echo Erreur lors de l'execution de l'application
    pause
    exit /b 1
)

echo Application fermee
pause
