@echo off
echo 🎨 === COMPILATION ET EXECUTION JAVAFX ===
echo.

REM Verifier si Java est installe
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Erreur: Java n'est pas installe ou n'est pas dans le PATH
    pause
    exit /b 1
)

REM Afficher la version Java
echo ☕ Version Java:
java -version 2>&1 | findstr "version"

REM Creer les repertoires necessaires
if not exist "target\classes" mkdir "target\classes"
if not exist "logs" mkdir "logs"

echo.
echo 📦 Compilation des classes Java...

REM Compiler les classes de base
javac -d target\classes ^
      src\main\java\com\drawing\model\Point.java ^
      src\main\java\com\drawing\patterns\ShapeFactorySimple.java ^
      src\main\java\com\drawing\SimpleDrawingApp.java

if %errorlevel% neq 0 (
    echo ❌ Erreur de compilation
    pause
    exit /b 1
)

echo ✅ Compilation reussie!
echo.
echo 🚀 Lancement de l'application JavaFX...

REM Essayer differents chemins pour JavaFX
set JAVAFX_PATHS="C:\Program Files\Java\javafx-sdk-19\lib" "C:\javafx\lib" "C:\Program Files\JavaFX\lib"

set JAVAFX_FOUND=false
for %%p in (%JAVAFX_PATHS%) do (
    if exist %%p (
        echo ✅ JavaFX trouve dans: %%p
        set JAVAFX_PATH=%%p
        set JAVAFX_FOUND=true
        goto :found
    )
)

:found
if "%JAVAFX_FOUND%"=="true" (
    echo 🎯 Execution avec JavaFX...
    java --module-path %JAVAFX_PATH% ^
         --add-modules javafx.controls,javafx.fxml ^
         -cp target\classes ^
         com.drawing.SimpleDrawingApp
) else (
    echo ⚠️  JavaFX non trouve dans les chemins standards
    echo 🔄 Tentative d'execution sans module-path...
    
    REM Essayer sans module-path
    java -cp target\classes com.drawing.SimpleDrawingApp
    
    if %errorlevel% neq 0 (
        echo ❌ Impossible d'executer l'application JavaFX
        echo.
        echo 📋 Solutions possibles:
        echo 1. Telecharger JavaFX SDK depuis https://openjfx.io/
        echo 2. Installer dans C:\Program Files\Java\javafx-sdk-19\
        echo 3. Utiliser un JDK qui inclut JavaFX
        echo.
        echo 🔄 Execution de la demonstration console a la place...
        java -cp target\classes com.drawing.patterns.ShapeFactorySimple
    )
)

echo.
echo 🎉 Execution terminee!
pause
