# 🚀 Guide de Démarrage Rapide

## ⚡ Exécution Immédiate

### Option 1: Script Automatique (Recommandé)
```bash
./run-demos.sh
```
Ce script propose un menu interactif pour exécuter toutes les démonstrations.

### Option 2: Commandes <PERSON>

#### Tests Unitaires
```bash
javac -d target/classes src/main/java/com/drawing/model/PointSimple.java
javac -d target/test-classes -cp target/classes src/test/java/com/drawing/model/PointSimpleTest.java
java -ea -cp "target/classes;target/test-classes" com.drawing.model.PointSimpleTest
```

#### Factory Pattern
```bash
javac -d target/classes -cp target/classes src/main/java/com/drawing/patterns/ShapeFactorySimple.java
java -cp target/classes com.drawing.patterns.ShapeFactorySimple
```

#### Strategy Pattern
```bash
javac -d target/classes -cp target/classes src/main/java/com/drawing/patterns/LoggingStrategySimple.java
java -cp target/classes com.drawing.patterns.LoggingStrategySimple
```

#### Command Pattern
```bash
javac -d target/classes -cp target/classes src/main/java/com/drawing/patterns/CommandPatternSimple.java
java -cp target/classes com.drawing.patterns.CommandPatternSimple
```

#### Démonstration Complète
```bash
javac -d target/classes -cp target/classes src/main/java/com/drawing/demo/DrawingAppDemo.java
java -cp target/classes com.drawing.demo.DrawingAppDemo
```

## 📋 Prérequis Minimum

- ✅ **Java 17+** (Java 21.0.7 détecté)
- ✅ **Bash** ou terminal compatible
- ✅ **Permissions d'écriture** dans le répertoire

## 📁 Fichiers Générés

Après exécution, vous trouverez :
- `target/classes/` - Classes Java compilées
- `logs/*.log` - Fichiers de journalisation
- `data/` - Base de données (si application complète)

## 🎯 Ce que Vous Verrez

### 1. Tests Unitaires ✅
```
=== Tests pour PointSimple ===
Test Constructor... ✅ PASSÉ
Test Getters... ✅ PASSÉ
...
Tests exécutés: 9
Tests réussis: 9
✅ Tous les tests sont passés!
```

### 2. Factory Pattern 🏭
```
=== Démonstration du Factory Pattern ===
1. Création d'un rectangle:
Affichage du Rectangle[topLeft=Point(0,00, 0,00), width=4,00, height=3,00]
  Aire: 12.0
  Périmètre: 14.0
...
```

### 3. Strategy Pattern 📝
```
=== Démonstration du Strategy Pattern pour Logging ===
1. Test avec stratégie Console:
[2025-06-03 03:28:58] [INFO] Message de test
...
```

### 4. Command Pattern ⚡
```
=== Démonstration du Command Pattern ===
1. État initial du dessin:
=== Dessin: Mon Dessin ===
Aucune forme dans le dessin
...
```

### 5. Démonstration Complète 🎨
```
🎨 === DÉMONSTRATION COMPLÈTE DE L'APPLICATION DE DESSIN ===
📋 1. INITIALISATION DE L'APPLICATION
📋 2. CONFIGURATION DU LOGGING
...
✅ Design Patterns démontrés:
   • Factory Pattern - Création des formes géométriques
   • Strategy Pattern - Stratégies de journalisation
   • Command Pattern - Gestion des actions avec undo/redo
```

## 🔧 Dépannage Rapide

### Erreur: "java: command not found"
```bash
# Vérifier l'installation Java
which java
java --version

# Si Java n'est pas installé:
# Ubuntu/Debian: sudo apt install openjdk-17-jdk
# macOS: brew install openjdk@17
# Windows: Télécharger depuis oracle.com
```

### Erreur: "Could not find or load main class"
```bash
# Vérifier la compilation
ls -la target/classes/com/drawing/model/
# Si vide, recompiler:
javac -d target/classes src/main/java/com/drawing/model/PointSimple.java
```

### Erreur: "Permission denied"
```bash
# Rendre le script exécutable
chmod +x run-demos.sh
```

## 📚 Documentation Complète

- `README.md` - Documentation principale
- `INSTALLATION.md` - Guide d'installation détaillé
- `SYNTHESE.md` - Synthèse complète du projet

## 🎓 Objectifs Pédagogiques

Ce projet démontre :
- ✅ **Factory Pattern** - Création d'objets
- ✅ **Strategy Pattern** - Algorithmes interchangeables  
- ✅ **Command Pattern** - Actions avec undo/redo
- ✅ **MVC Pattern** - Architecture modulaire
- ✅ **Tests Unitaires** - Validation du code
- ✅ **Clean Code** - Bonnes pratiques

## ⏱️ Temps d'Exécution

- Tests unitaires: ~2 secondes
- Chaque démonstration: ~5-10 secondes
- Démonstration complète: ~30 secondes
- **Total**: ~1 minute pour tout voir

## 🎉 Prêt à Commencer ?

```bash
# Commande unique pour tout voir
./run-demos.sh
```

Ou pour la démonstration complète directement :
```bash
java -cp target/classes com.drawing.demo.DrawingAppDemo
```

**Bon apprentissage ! 🚀**
