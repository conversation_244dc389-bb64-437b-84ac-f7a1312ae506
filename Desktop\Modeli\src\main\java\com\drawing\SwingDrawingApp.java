package com.drawing;

import javax.swing.*;
import java.awt.*;
import java.awt.event.*;
import java.awt.geom.*;
import java.util.ArrayList;
import java.util.List;
import com.drawing.model.Point;
import com.drawing.patterns.ShapeFactorySimple;

/**
 * Application Swing pour dessiner des formes géométriques
 * Alternative à JavaFX qui fonctionne avec Java standard
 */
public class SwingDrawingApp extends JFrame {
    
    private DrawingPanel drawingPanel;
    private ShapeFactorySimple.ShapeType currentShapeType = ShapeFactorySimple.ShapeType.RECTANGLE;
    private Color currentColor = Color.BLACK;
    private JLabel statusLabel;
    
    // Liste des formes dessinées
    private List<DrawnShape> shapes = new ArrayList<>();
    
    // Classe pour stocker une forme dessinée
    private static class DrawnShape {
        Point start, end;
        ShapeFactorySimple.ShapeType type;
        Color color;
        
        DrawnShape(Point start, Point end, ShapeFactorySimple.ShapeType type, Color color) {
            this.start = start;
            this.end = end;
            this.type = type;
            this.color = color;
        }
    }
    
    public SwingDrawingApp() {
        setTitle("🎨 Application de Dessin Géométrique - Design Patterns (Swing)");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1000, 700);
        setLocationRelativeTo(null);
        
        initializeUI();
        
        System.out.println("🎨 Application Swing démarrée avec succès!");
        System.out.println("✅ Design Patterns implémentés:");
        System.out.println("   • Factory Pattern - Création des formes");
        System.out.println("   • Strategy Pattern - Différents types de formes");
        System.out.println("   • MVC Pattern - Séparation des responsabilités");
    }
    
    private void initializeUI() {
        setLayout(new BorderPane());
        
        // Barre d'outils en haut
        add(createToolBar(), BorderLayout.NORTH);
        
        // Zone de dessin au centre
        drawingPanel = new DrawingPanel();
        add(new JScrollPane(drawingPanel), BorderLayout.CENTER);
        
        // Barre de statut en bas
        statusLabel = new JLabel("🎯 Prêt - Sélectionnez une forme et dessinez!");
        statusLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
        statusLabel.setBackground(Color.LIGHT_GRAY);
        statusLabel.setOpaque(true);
        add(statusLabel, BorderLayout.SOUTH);
    }
    
    private JPanel createToolBar() {
        JPanel toolBar = new JPanel(new FlowLayout(FlowLayout.LEFT));
        toolBar.setBorder(BorderFactory.createEtchedBorder());
        
        // Titre
        JLabel titleLabel = new JLabel("🎨 Formes:");
        titleLabel.setFont(titleLabel.getFont().deriveFont(Font.BOLD));
        toolBar.add(titleLabel);
        
        toolBar.add(new JSeparator(SwingConstants.VERTICAL));
        
        // Boutons de sélection de formes
        ButtonGroup shapeGroup = new ButtonGroup();
        
        JRadioButton rectButton = new JRadioButton("📐 Rectangle", true);
        rectButton.addActionListener(e -> {
            currentShapeType = ShapeFactorySimple.ShapeType.RECTANGLE;
            updateStatus("Rectangle sélectionné");
        });
        shapeGroup.add(rectButton);
        toolBar.add(rectButton);
        
        JRadioButton circleButton = new JRadioButton("⭕ Cercle");
        circleButton.addActionListener(e -> {
            currentShapeType = ShapeFactorySimple.ShapeType.CIRCLE;
            updateStatus("Cercle sélectionné");
        });
        shapeGroup.add(circleButton);
        toolBar.add(circleButton);
        
        JRadioButton lineButton = new JRadioButton("📏 Ligne");
        lineButton.addActionListener(e -> {
            currentShapeType = ShapeFactorySimple.ShapeType.LINE;
            updateStatus("Ligne sélectionnée");
        });
        shapeGroup.add(lineButton);
        toolBar.add(lineButton);
        
        toolBar.add(new JSeparator(SwingConstants.VERTICAL));
        
        // Sélecteur de couleur
        JLabel colorLabel = new JLabel("🎨 Couleur:");
        toolBar.add(colorLabel);
        
        JButton colorButton = new JButton();
        colorButton.setBackground(Color.BLACK);
        colorButton.setPreferredSize(new Dimension(30, 25));
        colorButton.addActionListener(e -> {
            Color newColor = JColorChooser.showDialog(this, "Choisir une couleur", currentColor);
            if (newColor != null) {
                currentColor = newColor;
                colorButton.setBackground(currentColor);
                updateStatus("Couleur changée: " + getColorName(currentColor));
            }
        });
        toolBar.add(colorButton);
        
        toolBar.add(new JSeparator(SwingConstants.VERTICAL));
        
        // Bouton effacer
        JButton clearButton = new JButton("🗑 Effacer");
        clearButton.addActionListener(e -> {
            shapes.clear();
            drawingPanel.repaint();
            updateStatus("Canvas effacé");
        });
        toolBar.add(clearButton);
        
        // Bouton démo
        JButton demoButton = new JButton("🚀 Démo");
        demoButton.addActionListener(e -> runDemo());
        toolBar.add(demoButton);
        
        return toolBar;
    }
    
    private void updateStatus(String message) {
        statusLabel.setText("🎯 " + message);
        System.out.println("✅ " + message);
    }
    
    private String getColorName(Color color) {
        if (color.equals(Color.BLACK)) return "Noir";
        if (color.equals(Color.RED)) return "Rouge";
        if (color.equals(Color.BLUE)) return "Bleu";
        if (color.equals(Color.GREEN)) return "Vert";
        if (color.equals(Color.YELLOW)) return "Jaune";
        return "Personnalisée";
    }
    
    private void runDemo() {
        System.out.println("🚀 === DÉMONSTRATION AUTOMATIQUE ===");
        
        shapes.clear();
        
        // Créer des formes de démonstration
        shapes.add(new DrawnShape(
            new Point(50, 80), new Point(200, 180),
            ShapeFactorySimple.ShapeType.RECTANGLE, Color.RED));
        
        shapes.add(new DrawnShape(
            new Point(250, 130), new Point(350, 180),
            ShapeFactorySimple.ShapeType.CIRCLE, Color.BLUE));
        
        shapes.add(new DrawnShape(
            new Point(400, 80), new Point(550, 180),
            ShapeFactorySimple.ShapeType.LINE, Color.GREEN));
        
        shapes.add(new DrawnShape(
            new Point(50, 250), new Point(150, 330),
            ShapeFactorySimple.ShapeType.RECTANGLE, Color.MAGENTA));
        
        shapes.add(new DrawnShape(
            new Point(200, 280), new Point(280, 320),
            ShapeFactorySimple.ShapeType.CIRCLE, Color.ORANGE));
        
        drawingPanel.repaint();
        updateStatus("Démonstration terminée - " + shapes.size() + " formes créées");
        
        System.out.println("🎉 Démonstration terminée!");
    }
    
    // Panel de dessin personnalisé
    private class DrawingPanel extends JPanel {
        private Point startPoint;
        private Point currentPoint;
        private boolean isDrawing = false;
        
        public DrawingPanel() {
            setBackground(Color.WHITE);
            setPreferredSize(new Dimension(800, 500));
            
            MouseAdapter mouseHandler = new MouseAdapter() {
                @Override
                public void mousePressed(MouseEvent e) {
                    startPoint = new Point(e.getX(), e.getY());
                    isDrawing = true;
                    updateStatus("Début du dessin à: (" + e.getX() + ", " + e.getY() + ")");
                }
                
                @Override
                public void mouseDragged(MouseEvent e) {
                    if (isDrawing) {
                        currentPoint = new Point(e.getX(), e.getY());
                        repaint();
                    }
                }
                
                @Override
                public void mouseReleased(MouseEvent e) {
                    if (isDrawing && startPoint != null) {
                        Point endPoint = new Point(e.getX(), e.getY());
                        shapes.add(new DrawnShape(startPoint, endPoint, currentShapeType, currentColor));
                        updateStatus("Forme créée: " + currentShapeType.getDisplayName() + 
                                   " de (" + (int)startPoint.getX() + "," + (int)startPoint.getY() + 
                                   ") à (" + e.getX() + "," + e.getY() + ")");
                        repaint();
                    }
                    isDrawing = false;
                    startPoint = null;
                    currentPoint = null;
                }
                
                @Override
                public void mouseMoved(MouseEvent e) {
                    statusLabel.setText("🎯 Position: (" + e.getX() + ", " + e.getY() + ")");
                }
            };
            
            addMouseListener(mouseHandler);
            addMouseMotionListener(mouseHandler);
        }
        
        @Override
        protected void paintComponent(Graphics g) {
            super.paintComponent(g);
            Graphics2D g2d = (Graphics2D) g;
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            
            // Dessiner toutes les formes sauvegardées
            for (DrawnShape shape : shapes) {
                drawShape(g2d, shape.start, shape.end, shape.type, shape.color, false);
            }
            
            // Dessiner la prévisualisation si en cours de dessin
            if (isDrawing && startPoint != null && currentPoint != null) {
                drawShape(g2d, startPoint, currentPoint, currentShapeType, currentColor, true);
            }
            
            // Message d'accueil si aucune forme
            if (shapes.isEmpty() && !isDrawing) {
                g2d.setColor(Color.GRAY);
                g2d.drawString("🎯 Cliquez et glissez pour dessiner des formes!", 20, 30);
                g2d.drawString("✨ Utilisez la barre d'outils pour changer de forme et de couleur", 20, 50);
            }
        }
        
        private void drawShape(Graphics2D g2d, Point start, Point end, 
                              ShapeFactorySimple.ShapeType type, Color color, boolean preview) {
            g2d.setColor(color);
            g2d.setStroke(new BasicStroke(preview ? 1 : 2, BasicStroke.CAP_ROUND, BasicStroke.JOIN_ROUND));
            
            if (preview) {
                g2d.setStroke(new BasicStroke(1, BasicStroke.CAP_ROUND, BasicStroke.JOIN_ROUND, 
                                            0, new float[]{5}, 0));
            }
            
            switch (type) {
                case RECTANGLE:
                    int width = (int) Math.abs(end.getX() - start.getX());
                    int height = (int) Math.abs(end.getY() - start.getY());
                    int x = (int) Math.min(start.getX(), end.getX());
                    int y = (int) Math.min(start.getY(), end.getY());
                    g2d.drawRect(x, y, width, height);
                    break;
                    
                case CIRCLE:
                    double radius = start.distanceTo(end);
                    int diameter = (int) (radius * 2);
                    g2d.drawOval((int)(start.getX() - radius), (int)(start.getY() - radius), 
                               diameter, diameter);
                    break;
                    
                case LINE:
                    g2d.drawLine((int)start.getX(), (int)start.getY(), 
                               (int)end.getX(), (int)end.getY());
                    break;
            }
        }
    }
    
    public static void main(String[] args) {
        System.out.println("🚀 Lancement de l'application Swing...");
        
        // Utiliser le look and feel du système
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
        } catch (Exception e) {
            System.out.println("⚠️ Impossible de charger le look and feel du système");
        }
        
        SwingUtilities.invokeLater(() -> {
            new SwingDrawingApp().setVisible(true);
        });
    }
}
