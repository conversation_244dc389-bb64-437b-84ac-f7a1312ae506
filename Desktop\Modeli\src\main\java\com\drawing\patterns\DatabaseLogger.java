package com.drawing.patterns;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;

/**
 * Implémentation de la stratégie de journalisation dans une base de données
 */
public class DatabaseLogger implements LoggingStrategy {
    
    private final DatabaseManager dbManager;
    private boolean isAvailable;
    
    public DatabaseLogger() {
        this.dbManager = DatabaseManager.getInstance();
        this.isAvailable = initializeLogTable();
    }
    
    private boolean initializeLogTable() {
        try {
            Connection conn = dbManager.getConnection();
            if (conn == null) {
                return false;
            }
            
            // Créer la table de logs si elle n'existe pas
            String createTableSQL = """
                CREATE TABLE IF NOT EXISTS logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME NOT NULL,
                    level VARCHAR(10) NOT NULL,
                    message TEXT NOT NULL,
                    exception TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
                """;
            
            try (PreparedStatement stmt = conn.prepareStatement(createTableSQL)) {
                stmt.executeUpdate();
            }
            
            // Insérer un message de démarrage
            insertLog(LogLevel.INFO, "=== Session de journalisation en base démarrée ===", null);
            
            return true;
        } catch (SQLException e) {
            System.err.println("Erreur lors de l'initialisation de la table de logs: " + e.getMessage());
            return false;
        }
    }
    
    @Override
    public void log(String message) {
        log(LogLevel.INFO, message);
    }
    
    @Override
    public void log(LogLevel level, String message) {
        log(level, message, null);
    }
    
    @Override
    public void log(LogLevel level, String message, Throwable throwable) {
        if (!isAvailable) {
            return;
        }
        
        String exceptionInfo = null;
        if (throwable != null) {
            StringBuilder sb = new StringBuilder();
            sb.append(throwable.getClass().getSimpleName()).append(": ").append(throwable.getMessage());
            
            if (level == LogLevel.ERROR) {
                sb.append("\nStack trace:\n");
                for (StackTraceElement element : throwable.getStackTrace()) {
                    sb.append("  ").append(element.toString()).append("\n");
                }
            }
            exceptionInfo = sb.toString();
        }
        
        insertLog(level, message, exceptionInfo);
    }
    
    @Override
    public void close() {
        if (isAvailable) {
            insertLog(LogLevel.INFO, "=== Session de journalisation en base terminée ===", null);
        }
        // La connexion à la base est gérée par le DatabaseManager (Singleton)
    }
    
    @Override
    public String getStrategyName() {
        return "Base de données";
    }
    
    @Override
    public boolean isAvailable() {
        return isAvailable && dbManager.isConnected();
    }
    
    private void insertLog(LogLevel level, String message, String exception) {
        try {
            Connection conn = dbManager.getConnection();
            if (conn == null) {
                isAvailable = false;
                return;
            }
            
            String insertSQL = """
                INSERT INTO logs (timestamp, level, message, exception) 
                VALUES (?, ?, ?, ?)
                """;
            
            try (PreparedStatement stmt = conn.prepareStatement(insertSQL)) {
                stmt.setTimestamp(1, Timestamp.valueOf(LocalDateTime.now()));
                stmt.setString(2, level.getName());
                stmt.setString(3, message);
                stmt.setString(4, exception);
                
                stmt.executeUpdate();
            }
            
        } catch (SQLException e) {
            System.err.println("Erreur lors de l'insertion du log en base: " + e.getMessage());
            isAvailable = false;
        }
    }
}
