#!/bin/bash

echo "=== Test de compilation (sans modules) ==="

# Vérifier si Java est installé
if ! command -v java &> /dev/null; then
    echo "Erreur: Java n'est pas installé ou n'est pas dans le PATH"
    exit 1
fi

# Créer les répertoires nécessaires
mkdir -p target/classes
mkdir -p data
mkdir -p logs

echo "Compilation des classes de base..."

# Compiler Point.java
echo "Compilation de Point.java..."
javac -d target/classes src/main/java/com/drawing/model/Point.java

if [ $? -ne 0 ]; then
    echo "Erreur lors de la compilation de Point.java"
    exit 1
fi

echo "Point.java compilé avec succès!"

# Compiler Bounds.java
echo "Compilation de Bounds.java..."
javac -d target/classes -cp target/classes src/main/java/com/drawing/model/Bounds.java

if [ $? -ne 0 ]; then
    echo "Erreur lors de la compilation de Bounds.java"
    exit 1
fi

echo "Bounds.java compilé avec succès!"

# Tester l'exécution d'une classe simple
echo "Test d'exécution..."
java -cp target/classes com.drawing.model.Point

echo ""
echo "Compilation de base réussie!"
echo "Les classes Point et Bounds sont fonctionnelles."
echo ""
echo "Pour une application complète, vous devez installer:"
echo "1. Maven"
echo "2. JavaFX SDK"
echo "3. Les dépendances listées dans pom.xml"
