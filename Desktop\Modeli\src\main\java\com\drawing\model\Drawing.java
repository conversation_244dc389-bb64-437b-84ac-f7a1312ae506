package com.drawing.model;

import javafx.scene.canvas.GraphicsContext;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Représente un dessin complet contenant plusieurs formes
 * Utilise le pattern Composite pour gérer les formes
 */
public class Drawing {
    private final String id;
    private String name;
    private final List<Shape> shapes;
    private final LocalDateTime createdAt;
    private LocalDateTime modifiedAt;

    @JsonCreator
    public Drawing(@JsonProperty("id") String id,
                   @JsonProperty("name") String name,
                   @JsonProperty("shapes") List<Shape> shapes,
                   @JsonProperty("createdAt") LocalDateTime createdAt,
                   @JsonProperty("modifiedAt") LocalDateTime modifiedAt) {
        this.id = id != null ? id : UUID.randomUUID().toString();
        this.name = name != null ? name : "Nouveau Dessin";
        this.shapes = shapes != null ? new ArrayList<>(shapes) : new ArrayList<>();
        this.createdAt = createdAt != null ? createdAt : LocalDateTime.now();
        this.modifiedAt = modifiedAt != null ? modifiedAt : LocalDateTime.now();
    }

    public Drawing(String name) {
        this(null, name, null, null, null);
    }

    public Drawing() {
        this("Nouveau Dessin");
    }

    /**
     * Ajoute une forme au dessin
     */
    public void addShape(Shape shape) {
        if (shape != null) {
            shapes.add(shape);
            updateModifiedTime();
        }
    }

    /**
     * Supprime une forme du dessin
     */
    public boolean removeShape(Shape shape) {
        boolean removed = shapes.remove(shape);
        if (removed) {
            updateModifiedTime();
        }
        return removed;
    }

    /**
     * Supprime toutes les formes du dessin
     */
    public void clearShapes() {
        shapes.clear();
        updateModifiedTime();
    }

    /**
     * Dessine toutes les formes sur le contexte graphique
     */
    public void draw(GraphicsContext gc) {
        for (Shape shape : shapes) {
            shape.draw(gc);
        }
    }

    /**
     * Trouve la forme qui contient le point donné (la plus récente si plusieurs)
     */
    public Shape findShapeAt(Point point) {
        // Parcourt la liste en sens inverse pour trouver la forme la plus récente
        for (int i = shapes.size() - 1; i >= 0; i--) {
            Shape shape = shapes.get(i);
            if (shape.contains(point)) {
                return shape;
            }
        }
        return null;
    }

    /**
     * Retourne les limites globales du dessin
     */
    public Bounds getBounds() {
        if (shapes.isEmpty()) {
            return new Bounds(0, 0, 0, 0);
        }

        double minX = Double.MAX_VALUE;
        double minY = Double.MAX_VALUE;
        double maxX = Double.MIN_VALUE;
        double maxY = Double.MIN_VALUE;

        for (Shape shape : shapes) {
            Bounds bounds = shape.getBounds();
            minX = Math.min(minX, bounds.getMinX());
            minY = Math.min(minY, bounds.getMinY());
            maxX = Math.max(maxX, bounds.getMaxX());
            maxY = Math.max(maxY, bounds.getMaxY());
        }

        return new Bounds(minX, minY, maxX, maxY);
    }

    /**
     * Clone le dessin
     */
    public Drawing clone() {
        List<Shape> clonedShapes = new ArrayList<>();
        for (Shape shape : shapes) {
            clonedShapes.add(shape.clone());
        }
        return new Drawing(null, name + " (Copie)", clonedShapes, createdAt, LocalDateTime.now());
    }

    /**
     * Convertit le dessin en JSON
     */
    public String toJson() {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            return "{}";
        }
    }

    private void updateModifiedTime() {
        this.modifiedAt = LocalDateTime.now();
    }

    // Getters et Setters
    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
        updateModifiedTime();
    }

    public List<Shape> getShapes() {
        return new ArrayList<>(shapes);
    }

    public int getShapeCount() {
        return shapes.size();
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public LocalDateTime getModifiedAt() {
        return modifiedAt;
    }

    @Override
    public String toString() {
        return String.format("Drawing[id=%s, name=%s, shapes=%d, created=%s]", 
                           id, name, shapes.size(), createdAt);
    }
}
