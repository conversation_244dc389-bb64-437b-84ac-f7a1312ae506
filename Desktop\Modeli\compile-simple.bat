@echo off
echo === Compilation simplifiee (sans dependances externes) ===

REM Verifier si Java est installe
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Erreur: Java n'est pas installe ou n'est pas dans le PATH
    pause
    exit /b 1
)

REM Creer les repertoires necessaires
if not exist "target\classes" mkdir "target\classes"
if not exist "data" mkdir "data"
if not exist "logs" mkdir "logs"

echo Compilation des sources Java (version simplifiee)...

REM Compiler sans les dependances externes pour tester la structure
javac -d target\classes ^
      -cp "." ^
      src\main\java\com\drawing\model\Point.java ^
      src\main\java\com\drawing\model\Bounds.java ^
      src\main\java\com\drawing\patterns\ShapeFactory.java

if %errorlevel% neq 0 (
    echo Erreur lors de la compilation
    pause
    exit /b 1
)

echo Compilation de base terminee avec succes!
echo.
echo Note: Cette compilation ne contient que les classes de base.
echo Pour une compilation complete, installez Maven et utilisez: mvn compile
pause
