# Guide d'Installation - Application de Dessin Géométrique

## État actuel du projet

✅ **Structure du projet créée**
✅ **Architecture avec design patterns implémentée**
✅ **Classes de base fonctionnelles** (test réussi avec PointSimple)
⚠️ **Dépendances externes requises pour l'application complète**

## Prérequis pour l'application complète

### 1. Java Development Kit (JDK)
- **Version requise**: JDK 17 ou supérieur
- **Statut**: ✅ Installé (Java 21.0.7 détecté)

### 2. JavaFX SDK
- **Version requise**: JavaFX 19 ou supérieur
- **Téléchargement**: https://openjfx.io/
- **Installation**: 
  - Télécharger JavaFX SDK
  - Extraire dans un répertoire (ex: `C:\Program Files\Java\javafx-sdk-19`)
  - Ajouter au PATH ou spécifier le chemin dans les scripts

### 3. Apache Maven
- **Version requise**: Maven 3.6 ou supérieur
- **Téléchargement**: https://maven.apache.org/download.cgi
- **Installation**:
  - Télécharger et extraire Maven
  - Ajouter `bin` au PATH
  - Configurer `MAVEN_HOME`

### 4. Dépendances Maven (automatiques avec Maven)
- SQLite JDBC Driver
- Jackson (JSON processing)
- SLF4J + Logback (logging)
- JUnit (tests)

## Installation étape par étape

### Étape 1: Installer Maven

#### Windows
```bash
# Télécharger Maven depuis https://maven.apache.org/download.cgi
# Extraire dans C:\Program Files\Apache\maven
# Ajouter au PATH: C:\Program Files\Apache\maven\bin
```

#### Linux/Mac
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install maven

# macOS avec Homebrew
brew install maven

# Vérifier l'installation
mvn --version
```

### Étape 2: Installer JavaFX

#### Téléchargement
1. Aller sur https://openjfx.io/
2. Télécharger JavaFX SDK 19+
3. Extraire dans un répertoire dédié

#### Configuration
```bash
# Exemple de chemin d'installation
# Windows: C:\Program Files\Java\javafx-sdk-19
# Linux: /opt/javafx-sdk-19
# macOS: /Library/Java/javafx-sdk-19
```

### Étape 3: Compiler l'application

```bash
# Dans le répertoire du projet
cd Desktop/Modeli

# Restaurer le module-info.java
mv src/main/java/module-info.java.bak src/main/java/module-info.java

# Compiler avec Maven
mvn clean compile

# Ou compiler manuellement avec JavaFX
javac --module-path /path/to/javafx-sdk-19/lib \
      --add-modules javafx.controls,javafx.fxml \
      -d target/classes \
      -cp "lib/*" \
      src/main/java/module-info.java \
      src/main/java/com/drawing/**/*.java
```

### Étape 4: Exécuter l'application

```bash
# Avec Maven
mvn javafx:run

# Ou manuellement
java --module-path /path/to/javafx-sdk-19/lib \
     --add-modules javafx.controls,javafx.fxml \
     -cp "target/classes:lib/*" \
     com.drawing.DrawingApp
```

## Test de l'installation

### Test 1: Vérifier Java
```bash
java --version
# Doit afficher Java 17+
```

### Test 2: Vérifier Maven
```bash
mvn --version
# Doit afficher Maven 3.6+
```

### Test 3: Test de compilation de base
```bash
# Tester la classe simple (déjà fonctionnel)
javac -d target/classes src/main/java/com/drawing/model/PointSimple.java
java -cp target/classes com.drawing.model.PointSimple
```

### Test 4: Test complet avec Maven
```bash
mvn clean test
mvn javafx:run
```

## Structure des fichiers après installation

```
Desktop/Modeli/
├── src/main/java/           # Sources Java
├── src/main/resources/      # Ressources (FXML, config)
├── target/classes/          # Classes compilées
├── data/                    # Base de données SQLite
├── logs/                    # Fichiers de log
├── lib/                     # Dépendances (si compilation manuelle)
├── pom.xml                  # Configuration Maven
├── README.md                # Documentation
└── INSTALLATION.md          # Ce fichier
```

## Dépannage

### Erreur: "module not found"
- Vérifier que JavaFX est installé et dans le module-path
- Vérifier que toutes les dépendances Maven sont téléchargées

### Erreur: "mvn: command not found"
- Installer Maven et l'ajouter au PATH
- Redémarrer le terminal après installation

### Erreur de compilation JavaFX
- Vérifier la version de JavaFX (19+ recommandé)
- Vérifier le chemin vers JavaFX SDK
- S'assurer que les modules JavaFX sont spécifiés

### Base de données SQLite
- Le fichier `data/drawing_app.db` est créé automatiquement
- Vérifier les permissions d'écriture dans le répertoire

## Alternative: Version simplifiée

Si vous rencontrez des difficultés avec les dépendances, une version simplifiée est disponible:

```bash
# Compiler et tester la version de base
javac -d target/classes src/main/java/com/drawing/model/PointSimple.java
java -cp target/classes com.drawing.model.PointSimple
```

Cette version démontre la structure et les design patterns sans les dépendances externes.

## Support

Pour toute question ou problème d'installation:
1. Vérifier que tous les prérequis sont installés
2. Consulter les logs d'erreur
3. Tester étape par étape selon ce guide
