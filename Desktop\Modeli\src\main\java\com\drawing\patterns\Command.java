package com.drawing.patterns;

/**
 * Interface Command pour le pattern Command
 * Permet d'encapsuler les actions utilisateur et de supporter undo/redo
 */
public interface Command {
    
    /**
     * Exécute la commande
     */
    void execute();
    
    /**
     * Annule la commande (undo)
     */
    void undo();
    
    /**
     * Refait la commande (redo)
     */
    default void redo() {
        execute();
    }
    
    /**
     * Retourne une description de la commande
     */
    String getDescription();
    
    /**
     * Vérifie si la commande peut être annulée
     */
    default boolean canUndo() {
        return true;
    }
    
    /**
     * Vérifie si la commande peut être refaite
     */
    default boolean canRedo() {
        return true;
    }
}
