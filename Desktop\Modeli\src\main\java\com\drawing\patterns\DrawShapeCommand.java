package com.drawing.patterns;

import com.drawing.model.Drawing;
import com.drawing.model.Shape;

/**
 * Commande pour dessiner une forme
 */
public class DrawShapeCommand implements Command {
    
    private final Drawing drawing;
    private final Shape shape;
    private boolean executed;
    
    public DrawShapeCommand(Drawing drawing, Shape shape) {
        this.drawing = drawing;
        this.shape = shape;
        this.executed = false;
    }
    
    @Override
    public void execute() {
        if (!executed) {
            drawing.addShape(shape);
            executed = true;
        }
    }
    
    @Override
    public void undo() {
        if (executed) {
            drawing.removeShape(shape);
            executed = false;
        }
    }
    
    @Override
    public void redo() {
        execute();
    }
    
    @Override
    public String getDescription() {
        return "Dessiner " + shape.getType() + " " + shape.toString();
    }
    
    @Override
    public boolean canUndo() {
        return executed;
    }
    
    @Override
    public boolean canRedo() {
        return !executed;
    }
    
    public Shape getShape() {
        return shape;
    }
    
    public Drawing getDrawing() {
        return drawing;
    }
    
    public boolean isExecuted() {
        return executed;
    }
}
