#!/bin/bash

echo "🎨 === EXÉCUTION DE TOUTES LES DÉMONSTRATIONS ==="
echo

# Vérifier si Java est installé
if ! command -v java &> /dev/null; then
    echo "❌ Erreur: Java n'est pas installé ou n'est pas dans le PATH"
    exit 1
fi

# Vérifier si les classes sont compilées
if [ ! -d "target/classes" ]; then
    echo "📦 Compilation des classes..."
    mkdir -p target/classes target/test-classes logs
    
    # Compiler les classes de base
    javac -d target/classes src/main/java/com/drawing/model/PointSimple.java
    javac -d target/classes -cp target/classes src/main/java/com/drawing/patterns/ShapeFactorySimple.java
    javac -d target/classes -cp target/classes src/main/java/com/drawing/patterns/LoggingStrategySimple.java
    javac -d target/classes -cp target/classes src/main/java/com/drawing/patterns/CommandPatternSimple.java
    javac -d target/classes -cp target/classes src/main/java/com/drawing/demo/DrawingAppDemo.java
    
    # Compiler les tests
    javac -d target/test-classes -cp target/classes src/test/java/com/drawing/model/PointSimpleTest.java
    
    echo "✅ Compilation terminée"
    echo
fi

# Menu interactif
while true; do
    echo "🎯 === MENU DES DÉMONSTRATIONS ==="
    echo "1. 🧪 Tests unitaires (PointSimpleTest)"
    echo "2. 🏭 Factory Pattern (ShapeFactorySimple)"
    echo "3. 📝 Strategy Pattern (LoggingStrategySimple)"
    echo "4. ⚡ Command Pattern (CommandPatternSimple)"
    echo "5. 🎨 Démonstration complète (DrawingAppDemo)"
    echo "6. 🔄 Exécuter toutes les démonstrations"
    echo "7. 📊 Afficher les logs générés"
    echo "8. 🧹 Nettoyer les fichiers générés"
    echo "9. ❌ Quitter"
    echo
    read -p "Choisissez une option (1-9): " choice
    echo

    case $choice in
        1)
            echo "🧪 === EXÉCUTION DES TESTS UNITAIRES ==="
            java -ea -cp "target/classes;target/test-classes" com.drawing.model.PointSimpleTest
            ;;
        2)
            echo "🏭 === DÉMONSTRATION DU FACTORY PATTERN ==="
            java -cp target/classes com.drawing.patterns.ShapeFactorySimple
            ;;
        3)
            echo "📝 === DÉMONSTRATION DU STRATEGY PATTERN ==="
            java -cp target/classes com.drawing.patterns.LoggingStrategySimple
            ;;
        4)
            echo "⚡ === DÉMONSTRATION DU COMMAND PATTERN ==="
            java -cp target/classes com.drawing.patterns.CommandPatternSimple
            ;;
        5)
            echo "🎨 === DÉMONSTRATION COMPLÈTE ==="
            java -cp target/classes com.drawing.demo.DrawingAppDemo
            ;;
        6)
            echo "🔄 === EXÉCUTION DE TOUTES LES DÉMONSTRATIONS ==="
            echo
            echo "1/5 - Tests unitaires..."
            java -ea -cp "target/classes;target/test-classes" com.drawing.model.PointSimpleTest
            echo
            echo "2/5 - Factory Pattern..."
            java -cp target/classes com.drawing.patterns.ShapeFactorySimple
            echo
            echo "3/5 - Strategy Pattern..."
            java -cp target/classes com.drawing.patterns.LoggingStrategySimple
            echo
            echo "4/5 - Command Pattern..."
            java -cp target/classes com.drawing.patterns.CommandPatternSimple
            echo
            echo "5/5 - Démonstration complète..."
            java -cp target/classes com.drawing.demo.DrawingAppDemo
            echo
            echo "✅ Toutes les démonstrations terminées!"
            ;;
        7)
            echo "📊 === LOGS GÉNÉRÉS ==="
            echo
            if [ -d "logs" ]; then
                echo "📁 Fichiers de log disponibles:"
                ls -la logs/
                echo
                for logfile in logs/*.log; do
                    if [ -f "$logfile" ]; then
                        echo "📄 Contenu de $logfile:"
                        echo "----------------------------------------"
                        cat "$logfile"
                        echo "----------------------------------------"
                        echo
                    fi
                done
            else
                echo "❌ Aucun fichier de log trouvé"
            fi
            ;;
        8)
            echo "🧹 === NETTOYAGE ==="
            read -p "Voulez-vous supprimer les fichiers générés? (y/N): " confirm
            if [[ $confirm =~ ^[Yy]$ ]]; then
                rm -rf target/classes target/test-classes logs/*.log
                echo "✅ Fichiers nettoyés"
            else
                echo "❌ Nettoyage annulé"
            fi
            ;;
        9)
            echo "👋 Au revoir!"
            exit 0
            ;;
        *)
            echo "❌ Option invalide. Veuillez choisir entre 1 et 9."
            ;;
    esac
    
    echo
    read -p "Appuyez sur Entrée pour continuer..."
    echo
done
