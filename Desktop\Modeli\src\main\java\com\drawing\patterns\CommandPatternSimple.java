package com.drawing.patterns;

import com.drawing.model.PointSimple;
import com.drawing.patterns.ShapeFactorySimple;
import java.util.ArrayList;
import java.util.List;

/**
 * Démonstration simplifiée du Command Pattern
 * Pour gérer les actions avec undo/redo
 */
public class CommandPatternSimple {
    
    /**
     * Interface Command
     */
    public interface Command {
        void execute();
        void undo();
        String getDescription();
        boolean canUndo();
    }
    
    /**
     * Récepteur - Gestionnaire de dessin simplifié
     */
    public static class SimpleDrawing {
        private final List<ShapeFactorySimple.SimpleShape> shapes = new ArrayList<>();
        private String name;
        
        public SimpleDrawing(String name) {
            this.name = name;
        }
        
        public void addShape(ShapeFactorySimple.SimpleShape shape) {
            shapes.add(shape);
            System.out.println("Forme ajoutée: " + shape.getDescription());
        }
        
        public boolean removeShape(ShapeFactorySimple.SimpleShape shape) {
            boolean removed = shapes.remove(shape);
            if (removed) {
                System.out.println("Forme supprimée: " + shape.getDescription());
            }
            return removed;
        }
        
        public void clear() {
            int count = shapes.size();
            shapes.clear();
            System.out.println("Toutes les formes supprimées (" + count + " formes)");
        }
        
        public void display() {
            System.out.println("\n=== Dessin: " + name + " ===");
            if (shapes.isEmpty()) {
                System.out.println("Aucune forme dans le dessin");
            } else {
                System.out.println("Formes dans le dessin (" + shapes.size() + "):");
                for (int i = 0; i < shapes.size(); i++) {
                    System.out.println("  " + (i + 1) + ". " + shapes.get(i).getDescription());
                }
            }
            System.out.println("========================");
        }
        
        public int getShapeCount() {
            return shapes.size();
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
    }
    
    /**
     * Commande pour ajouter une forme
     */
    public static class AddShapeCommand implements Command {
        private final SimpleDrawing drawing;
        private final ShapeFactorySimple.SimpleShape shape;
        private boolean executed = false;
        
        public AddShapeCommand(SimpleDrawing drawing, ShapeFactorySimple.SimpleShape shape) {
            this.drawing = drawing;
            this.shape = shape;
        }
        
        @Override
        public void execute() {
            if (!executed) {
                drawing.addShape(shape);
                executed = true;
            }
        }
        
        @Override
        public void undo() {
            if (executed) {
                drawing.removeShape(shape);
                executed = false;
            }
        }
        
        @Override
        public String getDescription() {
            return "Ajouter " + shape.getType() + ": " + shape.getDescription();
        }
        
        @Override
        public boolean canUndo() {
            return executed;
        }
    }
    
    /**
     * Commande pour effacer le dessin
     */
    public static class ClearDrawingCommand implements Command {
        private final SimpleDrawing drawing;
        private List<ShapeFactorySimple.SimpleShape> savedShapes;
        private boolean executed = false;
        
        public ClearDrawingCommand(SimpleDrawing drawing) {
            this.drawing = drawing;
        }
        
        @Override
        public void execute() {
            if (!executed) {
                // Sauvegarder les formes actuelles pour le undo
                savedShapes = new ArrayList<>();
                for (int i = 0; i < drawing.getShapeCount(); i++) {
                    // Note: Dans une vraie implémentation, on aurait accès à la liste
                    // Ici on simule en sauvegardant le nombre
                }
                drawing.clear();
                executed = true;
            }
        }
        
        @Override
        public void undo() {
            if (executed && savedShapes != null) {
                // Restaurer les formes
                for (ShapeFactorySimple.SimpleShape shape : savedShapes) {
                    drawing.addShape(shape);
                }
                executed = false;
            }
        }
        
        @Override
        public String getDescription() {
            return "Effacer tout le dessin";
        }
        
        @Override
        public boolean canUndo() {
            return executed;
        }
    }
    
    /**
     * Gestionnaire de commandes (Invoker)
     */
    public static class CommandManager {
        private final List<Command> history = new ArrayList<>();
        private int currentIndex = -1;
        private final int maxHistorySize;
        
        public CommandManager(int maxHistorySize) {
            this.maxHistorySize = maxHistorySize;
        }
        
        public CommandManager() {
            this(50);
        }
        
        public void executeCommand(Command command) {
            // Supprimer les commandes après l'index actuel (pour le redo)
            if (currentIndex < history.size() - 1) {
                history.subList(currentIndex + 1, history.size()).clear();
            }
            
            // Exécuter la commande
            command.execute();
            
            // Ajouter à l'historique
            history.add(command);
            currentIndex++;
            
            // Limiter la taille de l'historique
            if (history.size() > maxHistorySize) {
                history.remove(0);
                currentIndex--;
            }
            
            System.out.println("Commande exécutée: " + command.getDescription());
        }
        
        public boolean undo() {
            if (canUndo()) {
                Command command = history.get(currentIndex);
                command.undo();
                currentIndex--;
                System.out.println("Commande annulée: " + command.getDescription());
                return true;
            }
            System.out.println("Aucune commande à annuler");
            return false;
        }
        
        public boolean redo() {
            if (canRedo()) {
                currentIndex++;
                Command command = history.get(currentIndex);
                command.execute();
                System.out.println("Commande rétablie: " + command.getDescription());
                return true;
            }
            System.out.println("Aucune commande à rétablir");
            return false;
        }
        
        public boolean canUndo() {
            return currentIndex >= 0 && 
                   currentIndex < history.size() && 
                   history.get(currentIndex).canUndo();
        }
        
        public boolean canRedo() {
            return currentIndex + 1 < history.size();
        }
        
        public String getUndoDescription() {
            if (canUndo()) {
                return history.get(currentIndex).getDescription();
            }
            return null;
        }
        
        public String getRedoDescription() {
            if (canRedo()) {
                return history.get(currentIndex + 1).getDescription();
            }
            return null;
        }
        
        public void showHistory() {
            System.out.println("\n=== Historique des commandes ===");
            if (history.isEmpty()) {
                System.out.println("Aucune commande dans l'historique");
            } else {
                for (int i = 0; i < history.size(); i++) {
                    String marker = (i == currentIndex) ? " -> " : "    ";
                    String status = (i <= currentIndex) ? "[Exécutée]" : "[Annulée]";
                    System.out.println(marker + (i + 1) + ". " + history.get(i).getDescription() + " " + status);
                }
            }
            System.out.println("===============================");
        }
        
        public void clearHistory() {
            history.clear();
            currentIndex = -1;
            System.out.println("Historique des commandes effacé");
        }
    }
    
    /**
     * Démonstration du Command Pattern
     */
    public static void main(String[] args) {
        System.out.println("=== Démonstration du Command Pattern ===");
        
        // Créer le récepteur
        SimpleDrawing drawing = new SimpleDrawing("Mon Dessin");
        
        // Créer le gestionnaire de commandes
        CommandManager commandManager = new CommandManager();
        
        // Créer quelques formes
        PointSimple p1 = new PointSimple(0, 0);
        PointSimple p2 = new PointSimple(4, 3);
        PointSimple p3 = new PointSimple(2, 2);
        PointSimple p4 = new PointSimple(5, 5);
        
        ShapeFactorySimple.SimpleShape rectangle = ShapeFactorySimple.createRectangle(p1, p2);
        ShapeFactorySimple.SimpleShape circle = ShapeFactorySimple.createCircle(p3, p4);
        ShapeFactorySimple.SimpleShape line = ShapeFactorySimple.createLine(p1, p2);
        
        System.out.println("\n1. État initial du dessin:");
        drawing.display();
        
        System.out.println("\n2. Ajout de formes avec des commandes:");
        
        // Créer et exécuter des commandes
        Command addRectangle = new AddShapeCommand(drawing, rectangle);
        Command addCircle = new AddShapeCommand(drawing, circle);
        Command addLine = new AddShapeCommand(drawing, line);
        
        commandManager.executeCommand(addRectangle);
        drawing.display();
        
        commandManager.executeCommand(addCircle);
        drawing.display();
        
        commandManager.executeCommand(addLine);
        drawing.display();
        
        System.out.println("\n3. Historique des commandes:");
        commandManager.showHistory();
        
        System.out.println("\n4. Test des opérations Undo:");
        commandManager.undo();
        drawing.display();
        
        commandManager.undo();
        drawing.display();
        
        System.out.println("\n5. Test des opérations Redo:");
        commandManager.redo();
        drawing.display();
        
        System.out.println("\n6. Historique après undo/redo:");
        commandManager.showHistory();
        
        System.out.println("\n7. Ajout d'une nouvelle commande (efface le redo):");
        Command addNewCircle = new AddShapeCommand(drawing, 
            ShapeFactorySimple.createCircle(new PointSimple(1, 1), new PointSimple(3, 3)));
        commandManager.executeCommand(addNewCircle);
        drawing.display();
        
        commandManager.showHistory();
        
        System.out.println("\n8. Test de la commande Clear:");
        Command clearCommand = new ClearDrawingCommand(drawing);
        commandManager.executeCommand(clearCommand);
        drawing.display();
        
        System.out.println("\n9. Undo de la commande Clear:");
        commandManager.undo();
        drawing.display();
        
        System.out.println("\n✅ Démonstration du Command Pattern terminée!");
    }
}
