package com.drawing.model;

import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Implémentation d'une ligne
 */
public class Line implements Shape {
    private final Point start;
    private final Point end;
    private Color color;

    @JsonCreator
    public Line(@JsonProperty("start") Point start, 
                @JsonProperty("end") Point end,
                @JsonProperty("color") Color color) {
        this.start = start;
        this.end = end;
        this.color = color != null ? color : Color.BLACK;
    }

    public Line(Point start, Point end) {
        this(start, end, Color.BLACK);
    }

    @Override
    public void draw(GraphicsContext gc) {
        gc.setStroke(color);
        gc.setLineWidth(2.0);
        gc.strokeLine(start.getX(), start.getY(), end.getX(), end.getY());
    }

    @Override
    public String getType() {
        return "line";
    }

    @Override
    public Color getColor() {
        return color;
    }

    @Override
    public void setColor(Color color) {
        this.color = color;
    }

    @Override
    public boolean contains(Point point) {
        // Vérifie si le point est proche de la ligne (tolérance de 5 pixels)
        double tolerance = 5.0;
        double distance = distanceFromPointToLine(point);
        return distance <= tolerance;
    }

    /**
     * Calcule la distance d'un point à cette ligne
     */
    private double distanceFromPointToLine(Point point) {
        double A = end.getY() - start.getY();
        double B = start.getX() - end.getX();
        double C = end.getX() * start.getY() - start.getX() * end.getY();
        
        return Math.abs(A * point.getX() + B * point.getY() + C) / 
               Math.sqrt(A * A + B * B);
    }

    @Override
    public Bounds getBounds() {
        double minX = Math.min(start.getX(), end.getX());
        double maxX = Math.max(start.getX(), end.getX());
        double minY = Math.min(start.getY(), end.getY());
        double maxY = Math.max(start.getY(), end.getY());
        return new Bounds(minX, minY, maxX, maxY);
    }

    @Override
    public Shape clone() {
        return new Line(start, end, color);
    }

    @Override
    public String toJson() {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            return "{}";
        }
    }

    // Getters
    public Point getStart() {
        return start;
    }

    public Point getEnd() {
        return end;
    }

    public double getLength() {
        return start.distanceTo(end);
    }

    @Override
    public String toString() {
        return String.format("Line[start=%s, end=%s, color=%s]", 
                           start, end, color);
    }
}
