package com.drawing.model;

/**
 * Version simplifiée de Point pour test de compilation
 * Représente un point avec des coordonnées x et y
 */
public class PointSimple {
    private final double x;
    private final double y;

    public PointSimple(double x, double y) {
        this.x = x;
        this.y = y;
    }

    public double getX() {
        return x;
    }

    public double getY() {
        return y;
    }

    /**
     * Calcule la distance entre ce point et un autre point
     */
    public double distanceTo(PointSimple other) {
        double dx = this.x - other.x;
        double dy = this.y - other.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        PointSimple point = (PointSimple) obj;
        return Double.compare(point.x, x) == 0 && Double.compare(point.y, y) == 0;
    }

    @Override
    public int hashCode() {
        return java.util.Objects.hash(x, y);
    }

    @Override
    public String toString() {
        return String.format("Point(%.2f, %.2f)", x, y);
    }

    // Méthode main pour tester
    public static void main(String[] args) {
        System.out.println("=== Test de la classe PointSimple ===");
        
        PointSimple p1 = new PointSimple(0, 0);
        PointSimple p2 = new PointSimple(3, 4);
        
        System.out.println("Point 1: " + p1);
        System.out.println("Point 2: " + p2);
        System.out.println("Distance: " + p1.distanceTo(p2));
        
        System.out.println("Test réussi!");
    }
}
