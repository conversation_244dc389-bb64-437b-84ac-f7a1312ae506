package com.drawing.view;

import com.drawing.model.Drawing;
import com.drawing.model.Point;
import com.drawing.model.Shape;
import com.drawing.patterns.ShapeFactory;
import com.drawing.patterns.DrawShapeCommand;
import com.drawing.patterns.CommandManager;
import com.drawing.service.LoggingService;

import javafx.scene.canvas.Canvas;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.input.MouseEvent;
import javafx.scene.paint.Color;

/**
 * Zone de dessin personnalisée
 * Gère l'interaction utilisateur pour dessiner des formes
 */
public class DrawingCanvas extends Canvas {
    
    private final Drawing drawing;
    private final CommandManager commandManager;
    private final LoggingService loggingService;
    
    private ShapeFactory.ShapeType currentShapeType;
    private Color currentColor;
    private Point startPoint;
    private boolean isDrawing;
    
    public DrawingCanvas(double width, double height, Drawing drawing, 
                        CommandManager commandManager, LoggingService loggingService) {
        super(width, height);
        this.drawing = drawing;
        this.commandManager = commandManager;
        this.loggingService = loggingService;
        this.currentShapeType = ShapeFactory.ShapeType.RECTANGLE;
        this.currentColor = Color.BLACK;
        this.isDrawing = false;
        
        initializeEventHandlers();
        redraw();
    }
    
    /**
     * Initialise les gestionnaires d'événements souris
     */
    private void initializeEventHandlers() {
        setOnMousePressed(this::handleMousePressed);
        setOnMouseDragged(this::handleMouseDragged);
        setOnMouseReleased(this::handleMouseReleased);
        setOnMouseMoved(this::handleMouseMoved);
    }
    
    /**
     * Gère le clic de souris (début du dessin)
     */
    private void handleMousePressed(MouseEvent event) {
        startPoint = new Point(event.getX(), event.getY());
        isDrawing = true;
        
        loggingService.logUserAction("Début dessin", 
            String.format("Type: %s, Position: (%.1f, %.1f)", 
                         currentShapeType.getDisplayName(), event.getX(), event.getY()));
    }
    
    /**
     * Gère le glissement de souris (prévisualisation)
     */
    private void handleMouseDragged(MouseEvent event) {
        if (isDrawing && startPoint != null) {
            Point currentPoint = new Point(event.getX(), event.getY());
            drawPreview(startPoint, currentPoint);
        }
    }
    
    /**
     * Gère le relâchement de souris (fin du dessin)
     */
    private void handleMouseReleased(MouseEvent event) {
        if (isDrawing && startPoint != null) {
            Point endPoint = new Point(event.getX(), event.getY());
            
            // Créer la forme finale
            Shape shape = ShapeFactory.createShape(currentShapeType, startPoint, endPoint, currentColor);
            
            // Utiliser le pattern Command pour ajouter la forme
            DrawShapeCommand command = new DrawShapeCommand(drawing, shape);
            commandManager.executeCommand(command);
            
            loggingService.logUserAction("Forme créée", 
                String.format("Type: %s, Début: (%.1f, %.1f), Fin: (%.1f, %.1f)", 
                             currentShapeType.getDisplayName(), 
                             startPoint.getX(), startPoint.getY(),
                             endPoint.getX(), endPoint.getY()));
            
            // Redessiner le canvas
            redraw();
        }
        
        isDrawing = false;
        startPoint = null;
    }
    
    /**
     * Gère le mouvement de souris (sans clic)
     */
    private void handleMouseMoved(MouseEvent event) {
        // Peut être utilisé pour afficher des informations sur la position
        // ou pour d'autres interactions futures
    }
    
    /**
     * Dessine une prévisualisation de la forme en cours de création
     */
    private void drawPreview(Point start, Point current) {
        // Redessiner le dessin existant
        redraw();
        
        // Dessiner la prévisualisation
        GraphicsContext gc = getGraphicsContext2D();
        gc.setStroke(currentColor);
        gc.setLineWidth(1.0);
        gc.getLineDashes().clear();
        gc.setLineDashes(5); // Ligne pointillée pour la prévisualisation
        
        try {
            Shape previewShape = ShapeFactory.createShape(currentShapeType, start, current, currentColor);
            previewShape.draw(gc);
        } catch (Exception e) {
            // Ignorer les erreurs de prévisualisation
        }
        
        gc.getLineDashes().clear(); // Remettre la ligne normale
    }
    
    /**
     * Redessine tout le canvas
     */
    public void redraw() {
        GraphicsContext gc = getGraphicsContext2D();
        
        // Effacer le canvas
        gc.clearRect(0, 0, getWidth(), getHeight());
        
        // Dessiner un fond blanc
        gc.setFill(Color.WHITE);
        gc.fillRect(0, 0, getWidth(), getHeight());
        
        // Dessiner une bordure
        gc.setStroke(Color.LIGHTGRAY);
        gc.setLineWidth(1.0);
        gc.strokeRect(0, 0, getWidth(), getHeight());
        
        // Dessiner toutes les formes du dessin
        drawing.draw(gc);
    }
    
    /**
     * Efface tout le dessin
     */
    public void clearDrawing() {
        drawing.clearShapes();
        commandManager.clearHistory();
        redraw();
        
        loggingService.logUserAction("Effacement", "Toutes les formes ont été supprimées");
    }
    
    /**
     * Trouve la forme à une position donnée
     */
    public Shape findShapeAt(double x, double y) {
        Point point = new Point(x, y);
        return drawing.findShapeAt(point);
    }
    
    // Getters et Setters
    public ShapeFactory.ShapeType getCurrentShapeType() {
        return currentShapeType;
    }
    
    public void setCurrentShapeType(ShapeFactory.ShapeType shapeType) {
        this.currentShapeType = shapeType;
        loggingService.logUserAction("Sélection forme", 
            "Type sélectionné: " + shapeType.getDisplayName());
    }
    
    public Color getCurrentColor() {
        return currentColor;
    }
    
    public void setCurrentColor(Color color) {
        this.currentColor = color;
        loggingService.logUserAction("Sélection couleur", 
            "Couleur sélectionnée: " + color.toString());
    }
    
    public Drawing getDrawing() {
        return drawing;
    }
    
    public boolean isDrawing() {
        return isDrawing;
    }
}
