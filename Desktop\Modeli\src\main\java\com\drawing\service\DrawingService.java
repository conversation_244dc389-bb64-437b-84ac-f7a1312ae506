package com.drawing.service;

import com.drawing.model.Drawing;
import com.drawing.patterns.DatabaseManager;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Service pour sauvegarder et charger les dessins
 */
public class DrawingService {
    
    private final DatabaseManager dbManager;
    private final ObjectMapper objectMapper;
    private final LoggingService loggingService;
    
    public DrawingService(LoggingService loggingService) {
        this.dbManager = DatabaseManager.getInstance();
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
        this.loggingService = loggingService;
    }
    
    /**
     * Sauvegarde un dessin en base de données
     */
    public boolean saveDrawing(Drawing drawing) {
        try {
            Connection conn = dbManager.getConnection();
            if (conn == null) {
                loggingService.logError("Impossible de sauvegarder: connexion à la base indisponible", null);
                return false;
            }
            
            String json = drawing.toJson();
            
            // Vérifier si le dessin existe déjà
            if (drawingExists(drawing.getId())) {
                return updateDrawing(drawing, json);
            } else {
                return insertDrawing(drawing, json);
            }
            
        } catch (Exception e) {
            loggingService.logError("Erreur lors de la sauvegarde du dessin: " + drawing.getName(), e);
            return false;
        }
    }
    
    /**
     * Insère un nouveau dessin
     */
    private boolean insertDrawing(Drawing drawing, String json) throws SQLException {
        String sql = """
            INSERT INTO drawings (id, name, data, created_at, modified_at) 
            VALUES (?, ?, ?, ?, ?)
            """;
        
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql)) {
            stmt.setString(1, drawing.getId());
            stmt.setString(2, drawing.getName());
            stmt.setString(3, json);
            stmt.setTimestamp(4, Timestamp.valueOf(drawing.getCreatedAt()));
            stmt.setTimestamp(5, Timestamp.valueOf(drawing.getModifiedAt()));
            
            int result = stmt.executeUpdate();
            
            if (result > 0) {
                loggingService.logUserAction("Sauvegarde", "Nouveau dessin sauvegardé: " + drawing.getName());
                return true;
            }
        }
        return false;
    }
    
    /**
     * Met à jour un dessin existant
     */
    private boolean updateDrawing(Drawing drawing, String json) throws SQLException {
        String sql = """
            UPDATE drawings 
            SET name = ?, data = ?, modified_at = ? 
            WHERE id = ?
            """;
        
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql)) {
            stmt.setString(1, drawing.getName());
            stmt.setString(2, json);
            stmt.setTimestamp(3, Timestamp.valueOf(drawing.getModifiedAt()));
            stmt.setString(4, drawing.getId());
            
            int result = stmt.executeUpdate();
            
            if (result > 0) {
                loggingService.logUserAction("Sauvegarde", "Dessin mis à jour: " + drawing.getName());
                return true;
            }
        }
        return false;
    }
    
    /**
     * Charge un dessin par son ID
     */
    public Drawing loadDrawing(String id) {
        try {
            Connection conn = dbManager.getConnection();
            if (conn == null) {
                loggingService.logError("Impossible de charger: connexion à la base indisponible", null);
                return null;
            }
            
            String sql = "SELECT * FROM drawings WHERE id = ?";
            
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, id);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        String json = rs.getString("data");
                        Drawing drawing = objectMapper.readValue(json, Drawing.class);
                        
                        loggingService.logUserAction("Chargement", "Dessin chargé: " + drawing.getName());
                        return drawing;
                    }
                }
            }
            
        } catch (Exception e) {
            loggingService.logError("Erreur lors du chargement du dessin ID: " + id, e);
        }
        
        return null;
    }
    
    /**
     * Retourne la liste de tous les dessins sauvegardés
     */
    public List<DrawingInfo> getAllDrawings() {
        List<DrawingInfo> drawings = new ArrayList<>();
        
        try {
            Connection conn = dbManager.getConnection();
            if (conn == null) {
                loggingService.logError("Impossible de lister: connexion à la base indisponible", null);
                return drawings;
            }
            
            String sql = "SELECT id, name, created_at, modified_at FROM drawings ORDER BY modified_at DESC";
            
            try (PreparedStatement stmt = conn.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                while (rs.next()) {
                    DrawingInfo info = new DrawingInfo(
                        rs.getString("id"),
                        rs.getString("name"),
                        rs.getTimestamp("created_at").toLocalDateTime(),
                        rs.getTimestamp("modified_at").toLocalDateTime()
                    );
                    drawings.add(info);
                }
            }
            
        } catch (SQLException e) {
            loggingService.logError("Erreur lors de la récupération de la liste des dessins", e);
        }
        
        return drawings;
    }
    
    /**
     * Supprime un dessin
     */
    public boolean deleteDrawing(String id) {
        try {
            Connection conn = dbManager.getConnection();
            if (conn == null) {
                loggingService.logError("Impossible de supprimer: connexion à la base indisponible", null);
                return false;
            }
            
            String sql = "DELETE FROM drawings WHERE id = ?";
            
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, id);
                int result = stmt.executeUpdate();
                
                if (result > 0) {
                    loggingService.logUserAction("Suppression", "Dessin supprimé ID: " + id);
                    return true;
                }
            }
            
        } catch (SQLException e) {
            loggingService.logError("Erreur lors de la suppression du dessin ID: " + id, e);
        }
        
        return false;
    }
    
    /**
     * Vérifie si un dessin existe
     */
    private boolean drawingExists(String id) throws SQLException {
        String sql = "SELECT 1 FROM drawings WHERE id = ?";
        
        try (PreparedStatement stmt = dbManager.getConnection().prepareStatement(sql)) {
            stmt.setString(1, id);
            
            try (ResultSet rs = stmt.executeQuery()) {
                return rs.next();
            }
        }
    }
    
    /**
     * Classe pour les informations de base d'un dessin
     */
    public static class DrawingInfo {
        private final String id;
        private final String name;
        private final LocalDateTime createdAt;
        private final LocalDateTime modifiedAt;
        
        public DrawingInfo(String id, String name, LocalDateTime createdAt, LocalDateTime modifiedAt) {
            this.id = id;
            this.name = name;
            this.createdAt = createdAt;
            this.modifiedAt = modifiedAt;
        }
        
        public String getId() { return id; }
        public String getName() { return name; }
        public LocalDateTime getCreatedAt() { return createdAt; }
        public LocalDateTime getModifiedAt() { return modifiedAt; }
        
        @Override
        public String toString() {
            return name + " (modifié le " + modifiedAt.toLocalDate() + ")";
        }
    }
}
