package com.drawing.patterns;

import com.drawing.model.PointSimple;

/**
 * Version simplifiée du Factory Pattern pour créer des formes géométriques
 * Démontre le pattern Factory sans dépendances externes
 */
public class ShapeFactorySimple {
    
    public enum ShapeType {
        RECTANGLE("Rectangle"),
        CIRCLE("Cercle"),
        LINE("Ligne");
        
        private final String displayName;
        
        ShapeType(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }

    /**
     * Interface simplifiée pour les formes
     */
    public interface SimpleShape {
        String getType();
        double getArea();
        double getPerimeter();
        String getDescription();
        void display();
    }

    /**
     * Implémentation simplifiée d'un rectangle
     */
    public static class SimpleRectangle implements SimpleShape {
        private final PointSimple topLeft;
        private final double width;
        private final double height;

        public SimpleRectangle(PointSimple topLeft, double width, double height) {
            this.topLeft = topLeft;
            this.width = Math.abs(width);
            this.height = Math.abs(height);
        }

        @Override
        public String getType() {
            return "Rectangle";
        }

        @Override
        public double getArea() {
            return width * height;
        }

        @Override
        public double getPerimeter() {
            return 2 * (width + height);
        }

        @Override
        public String getDescription() {
            return String.format("Rectangle[topLeft=%s, width=%.2f, height=%.2f]", 
                               topLeft, width, height);
        }

        @Override
        public void display() {
            System.out.println("Affichage du " + getDescription());
            System.out.println("  Aire: " + getArea());
            System.out.println("  Périmètre: " + getPerimeter());
        }

        public PointSimple getTopLeft() { return topLeft; }
        public double getWidth() { return width; }
        public double getHeight() { return height; }
    }

    /**
     * Implémentation simplifiée d'un cercle
     */
    public static class SimpleCircle implements SimpleShape {
        private final PointSimple center;
        private final double radius;

        public SimpleCircle(PointSimple center, double radius) {
            this.center = center;
            this.radius = Math.abs(radius);
        }

        @Override
        public String getType() {
            return "Circle";
        }

        @Override
        public double getArea() {
            return Math.PI * radius * radius;
        }

        @Override
        public double getPerimeter() {
            return 2 * Math.PI * radius;
        }

        @Override
        public String getDescription() {
            return String.format("Circle[center=%s, radius=%.2f]", center, radius);
        }

        @Override
        public void display() {
            System.out.println("Affichage du " + getDescription());
            System.out.println("  Aire: " + String.format("%.2f", getArea()));
            System.out.println("  Périmètre: " + String.format("%.2f", getPerimeter()));
        }

        public PointSimple getCenter() { return center; }
        public double getRadius() { return radius; }
    }

    /**
     * Implémentation simplifiée d'une ligne
     */
    public static class SimpleLine implements SimpleShape {
        private final PointSimple start;
        private final PointSimple end;

        public SimpleLine(PointSimple start, PointSimple end) {
            this.start = start;
            this.end = end;
        }

        @Override
        public String getType() {
            return "Line";
        }

        @Override
        public double getArea() {
            return 0; // Une ligne n'a pas d'aire
        }

        @Override
        public double getPerimeter() {
            return getLength(); // Pour une ligne, le "périmètre" est sa longueur
        }

        public double getLength() {
            return start.distanceTo(end);
        }

        @Override
        public String getDescription() {
            return String.format("Line[start=%s, end=%s, length=%.2f]", 
                               start, end, getLength());
        }

        @Override
        public void display() {
            System.out.println("Affichage de la " + getDescription());
            System.out.println("  Longueur: " + String.format("%.2f", getLength()));
        }

        public PointSimple getStart() { return start; }
        public PointSimple getEnd() { return end; }
    }

    /**
     * Méthodes Factory pour créer les formes
     */
    public static SimpleShape createRectangle(PointSimple topLeft, PointSimple bottomRight) {
        double width = Math.abs(bottomRight.getX() - topLeft.getX());
        double height = Math.abs(bottomRight.getY() - topLeft.getY());
        
        // Assurer que topLeft est vraiment en haut à gauche
        double actualTopLeftX = Math.min(topLeft.getX(), bottomRight.getX());
        double actualTopLeftY = Math.min(topLeft.getY(), bottomRight.getY());
        PointSimple actualTopLeft = new PointSimple(actualTopLeftX, actualTopLeftY);
        
        return new SimpleRectangle(actualTopLeft, width, height);
    }

    public static SimpleShape createCircle(PointSimple center, PointSimple edgePoint) {
        double radius = center.distanceTo(edgePoint);
        return new SimpleCircle(center, radius);
    }

    public static SimpleShape createLine(PointSimple start, PointSimple end) {
        return new SimpleLine(start, end);
    }

    public static SimpleShape createShape(ShapeType type, PointSimple point1, PointSimple point2) {
        switch (type) {
            case RECTANGLE:
                return createRectangle(point1, point2);
            case CIRCLE:
                return createCircle(point1, point2);
            case LINE:
                return createLine(point1, point2);
            default:
                throw new IllegalArgumentException("Type de forme non supporté: " + type);
        }
    }

    /**
     * Méthode de démonstration
     */
    public static void main(String[] args) {
        System.out.println("=== Démonstration du Factory Pattern ===");
        
        PointSimple p1 = new PointSimple(0, 0);
        PointSimple p2 = new PointSimple(4, 3);
        PointSimple p3 = new PointSimple(2, 2);
        PointSimple p4 = new PointSimple(5, 5);
        
        System.out.println("\n1. Création d'un rectangle:");
        SimpleShape rectangle = createRectangle(p1, p2);
        rectangle.display();
        
        System.out.println("\n2. Création d'un cercle:");
        SimpleShape circle = createCircle(p3, p4);
        circle.display();
        
        System.out.println("\n3. Création d'une ligne:");
        SimpleShape line = createLine(p1, p2);
        line.display();
        
        System.out.println("\n4. Utilisation du factory avec enum:");
        for (ShapeType type : ShapeType.values()) {
            System.out.println("\nCréation d'une forme de type: " + type.getDisplayName());
            SimpleShape shape = createShape(type, p1, p2);
            shape.display();
        }
        
        System.out.println("\n✅ Démonstration du Factory Pattern terminée!");
    }
}
