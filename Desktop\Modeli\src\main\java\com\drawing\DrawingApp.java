package com.drawing;

import com.drawing.controller.MainController;
import com.drawing.patterns.DatabaseManager;

import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Stage;

/**
 * Classe principale de l'application de dessin géométrique
 * Point d'entrée de l'application JavaFX
 */
public class DrawingApp extends Application {
    
    private static final String APP_TITLE = "Application de Dessin Géométrique";
    private static final String FXML_PATH = "/com/drawing/view/MainView.fxml";
    private static final double WINDOW_WIDTH = 1200;
    private static final double WINDOW_HEIGHT = 800;
    private static final double MIN_WINDOW_WIDTH = 800;
    private static final double MIN_WINDOW_HEIGHT = 600;
    
    @Override
    public void start(Stage primaryStage) {
        try {
            // Charger le fichier FXML
            FXMLLoader loader = new FXMLLoader(getClass().getResource(FXML_PATH));
            Parent root = loader.load();
            
            // Obtenir le contrôleur et lui passer la stage
            MainController controller = loader.getController();
            controller.setPrimaryStage(primaryStage);
            
            // Configurer la scène
            Scene scene = new Scene(root, WINDOW_WIDTH, WINDOW_HEIGHT);
            
            // Configurer la fenêtre principale
            primaryStage.setTitle(APP_TITLE);
            primaryStage.setScene(scene);
            primaryStage.setMinWidth(MIN_WINDOW_WIDTH);
            primaryStage.setMinHeight(MIN_WINDOW_HEIGHT);
            
            // Gestionnaire de fermeture
            primaryStage.setOnCloseRequest(event -> {
                // Fermer proprement la base de données
                DatabaseManager.getInstance().closeConnection();
                System.out.println("Application fermée proprement");
            });
            
            // Afficher la fenêtre
            primaryStage.show();
            
            System.out.println("Application de dessin géométrique démarrée");
            
        } catch (Exception e) {
            System.err.println("Erreur lors du démarrage de l'application: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Point d'entrée principal de l'application
     */
    public static void main(String[] args) {
        // Afficher les informations de démarrage
        System.out.println("=== Application de Dessin Géométrique ===");
        System.out.println("Démarrage de l'application...");
        
        // Vérifier la version de Java
        String javaVersion = System.getProperty("java.version");
        System.out.println("Version Java: " + javaVersion);
        
        // Vérifier JavaFX
        try {
            Class.forName("javafx.application.Application");
            System.out.println("JavaFX disponible");
        } catch (ClassNotFoundException e) {
            System.err.println("JavaFX non disponible!");
            System.exit(1);
        }
        
        // Lancer l'application JavaFX
        launch(args);
    }
    
    @Override
    public void stop() throws Exception {
        super.stop();
        
        // Nettoyage des ressources
        DatabaseManager.getInstance().closeConnection();
        
        System.out.println("Application arrêtée");
    }
}
