package com.drawing.controller;

import com.drawing.model.Drawing;
import com.drawing.patterns.CommandManager;
import com.drawing.patterns.ShapeFactory;
import com.drawing.service.DrawingService;
import com.drawing.service.LoggingService;
import com.drawing.view.DrawingCanvas;

import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.layout.Pane;
import javafx.scene.paint.Color;
import javafx.stage.FileChooser;
import javafx.stage.Stage;

import java.io.File;
import java.net.URL;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;

/**
 * Contrôleur principal de l'application
 * Implémente le pattern MVC
 */
public class MainController implements Initializable {
    
    @FXML private MenuBar menuBar;
    @FXML private Menu fileMenu;
    @FXML private Menu editMenu;
    @FXML private Menu viewMenu;
    @FXML private Menu helpMenu;
    
    @FXML private ToolBar toolBar;
    @FXML private ToggleGroup shapeToggleGroup;
    @FXML private RadioButton rectangleButton;
    @FXML private RadioButton circleButton;
    @FXML private RadioButton lineButton;
    @FXML private ColorPicker colorPicker;
    @FXML private Button undoButton;
    @FXML private Button redoButton;
    @FXML private Button clearButton;
    
    @FXML private Pane canvasContainer;
    @FXML private Label statusLabel;
    @FXML private Label coordinatesLabel;
    @FXML private ComboBox<LoggingService.StrategyType> loggingStrategyCombo;
    
    private DrawingCanvas drawingCanvas;
    private Drawing currentDrawing;
    private CommandManager commandManager;
    private LoggingService loggingService;
    private DrawingService drawingService;
    private Stage primaryStage;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        initializeServices();
        initializeDrawing();
        initializeCanvas();
        initializeControls();
        initializeEventHandlers();
        
        updateStatus("Application initialisée");
        loggingService.logUserAction("Démarrage", "Application démarrée");
    }
    
    /**
     * Initialise les services
     */
    private void initializeServices() {
        loggingService = new LoggingService();
        commandManager = new CommandManager();
        drawingService = new DrawingService(loggingService);
    }
    
    /**
     * Initialise le dessin
     */
    private void initializeDrawing() {
        currentDrawing = new Drawing("Nouveau Dessin");
    }
    
    /**
     * Initialise le canvas de dessin
     */
    private void initializeCanvas() {
        drawingCanvas = new DrawingCanvas(800, 600, currentDrawing, commandManager, loggingService);
        canvasContainer.getChildren().add(drawingCanvas);
        
        // Lier la taille du canvas au conteneur
        drawingCanvas.widthProperty().bind(canvasContainer.widthProperty());
        drawingCanvas.heightProperty().bind(canvasContainer.heightProperty());
        
        // Redessiner quand la taille change
        drawingCanvas.widthProperty().addListener((obs, oldVal, newVal) -> drawingCanvas.redraw());
        drawingCanvas.heightProperty().addListener((obs, oldVal, newVal) -> drawingCanvas.redraw());
    }
    
    /**
     * Initialise les contrôles
     */
    private void initializeControls() {
        // Initialiser les boutons de formes
        rectangleButton.setSelected(true);
        rectangleButton.setUserData(ShapeFactory.ShapeType.RECTANGLE);
        circleButton.setUserData(ShapeFactory.ShapeType.CIRCLE);
        lineButton.setUserData(ShapeFactory.ShapeType.LINE);
        
        // Initialiser le sélecteur de couleur
        colorPicker.setValue(Color.BLACK);
        
        // Initialiser le combo de stratégie de logging
        loggingStrategyCombo.getItems().addAll(LoggingService.StrategyType.values());
        loggingStrategyCombo.setValue(LoggingService.StrategyType.CONSOLE);
        
        // Mettre à jour l'état des boutons
        updateButtonStates();
    }
    
    /**
     * Initialise les gestionnaires d'événements
     */
    private void initializeEventHandlers() {
        // Gestionnaire pour le changement de forme
        shapeToggleGroup.selectedToggleProperty().addListener((obs, oldToggle, newToggle) -> {
            if (newToggle != null) {
                ShapeFactory.ShapeType shapeType = (ShapeFactory.ShapeType) newToggle.getUserData();
                drawingCanvas.setCurrentShapeType(shapeType);
                updateStatus("Forme sélectionnée: " + shapeType.getDisplayName());
            }
        });
        
        // Gestionnaire pour le changement de couleur
        colorPicker.setOnAction(e -> {
            drawingCanvas.setCurrentColor(colorPicker.getValue());
            updateStatus("Couleur sélectionnée: " + colorPicker.getValue());
        });
        
        // Gestionnaire pour le changement de stratégie de logging
        loggingStrategyCombo.setOnAction(e -> {
            LoggingService.StrategyType strategy = loggingStrategyCombo.getValue();
            if (strategy != null) {
                boolean success = loggingService.setStrategy(strategy);
                if (success) {
                    updateStatus("Stratégie de logging: " + strategy.getDisplayName());
                } else {
                    showAlert("Erreur", "Impossible de changer la stratégie de logging vers: " + strategy.getDisplayName());
                }
            }
        });
        
        // Gestionnaire pour les mouvements de souris sur le canvas
        drawingCanvas.setOnMouseMoved(e -> {
            coordinatesLabel.setText(String.format("X: %.0f, Y: %.0f", e.getX(), e.getY()));
        });
    }
    
    /**
     * Actions du menu Fichier
     */
    @FXML
    private void handleNewDrawing() {
        if (confirmUnsavedChanges()) {
            currentDrawing = new Drawing("Nouveau Dessin");
            drawingCanvas.clearDrawing();
            updateStatus("Nouveau dessin créé");
            loggingService.logUserAction("Nouveau", "Nouveau dessin créé");
        }
    }
    
    @FXML
    private void handleSaveDrawing() {
        boolean success = drawingService.saveDrawing(currentDrawing);
        if (success) {
            updateStatus("Dessin sauvegardé: " + currentDrawing.getName());
        } else {
            showAlert("Erreur", "Impossible de sauvegarder le dessin");
        }
    }
    
    @FXML
    private void handleLoadDrawing() {
        List<DrawingService.DrawingInfo> drawings = drawingService.getAllDrawings();
        if (drawings.isEmpty()) {
            showAlert("Information", "Aucun dessin sauvegardé trouvé");
            return;
        }
        
        ChoiceDialog<DrawingService.DrawingInfo> dialog = new ChoiceDialog<>(drawings.get(0), drawings);
        dialog.setTitle("Charger un dessin");
        dialog.setHeaderText("Sélectionnez un dessin à charger");
        dialog.setContentText("Dessin:");
        
        Optional<DrawingService.DrawingInfo> result = dialog.showAndWait();
        result.ifPresent(drawingInfo -> {
            Drawing loadedDrawing = drawingService.loadDrawing(drawingInfo.getId());
            if (loadedDrawing != null) {
                currentDrawing = loadedDrawing;
                drawingCanvas.redraw();
                updateStatus("Dessin chargé: " + loadedDrawing.getName());
            } else {
                showAlert("Erreur", "Impossible de charger le dessin");
            }
        });
    }
    
    @FXML
    private void handleExit() {
        if (confirmUnsavedChanges()) {
            loggingService.logUserAction("Fermeture", "Application fermée");
            loggingService.close();
            Platform.exit();
        }
    }
    
    /**
     * Actions du menu Édition
     */
    @FXML
    private void handleUndo() {
        if (commandManager.undo()) {
            drawingCanvas.redraw();
            updateStatus("Annulation: " + commandManager.getRedoDescription());
            updateButtonStates();
        }
    }
    
    @FXML
    private void handleRedo() {
        if (commandManager.redo()) {
            drawingCanvas.redraw();
            updateStatus("Rétablissement: " + commandManager.getUndoDescription());
            updateButtonStates();
        }
    }
    
    @FXML
    private void handleClear() {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Confirmation");
        alert.setHeaderText("Effacer le dessin");
        alert.setContentText("Êtes-vous sûr de vouloir effacer tout le dessin ?");
        
        Optional<ButtonType> result = alert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            drawingCanvas.clearDrawing();
            updateStatus("Dessin effacé");
            updateButtonStates();
        }
    }
    
    /**
     * Met à jour l'état des boutons
     */
    private void updateButtonStates() {
        undoButton.setDisable(!commandManager.canUndo());
        redoButton.setDisable(!commandManager.canRedo());
        
        // Mettre à jour les tooltips
        if (commandManager.canUndo()) {
            undoButton.setTooltip(new Tooltip("Annuler: " + commandManager.getUndoDescription()));
        } else {
            undoButton.setTooltip(new Tooltip("Aucune action à annuler"));
        }
        
        if (commandManager.canRedo()) {
            redoButton.setTooltip(new Tooltip("Rétablir: " + commandManager.getRedoDescription()));
        } else {
            redoButton.setTooltip(new Tooltip("Aucune action à rétablir"));
        }
    }
    
    /**
     * Met à jour la barre de statut
     */
    private void updateStatus(String message) {
        statusLabel.setText(message);
    }
    
    /**
     * Affiche une alerte
     */
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    /**
     * Confirme les changements non sauvegardés
     */
    private boolean confirmUnsavedChanges() {
        if (currentDrawing.getShapeCount() > 0) {
            Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
            alert.setTitle("Changements non sauvegardés");
            alert.setHeaderText("Le dessin actuel contient des modifications non sauvegardées");
            alert.setContentText("Voulez-vous continuer sans sauvegarder ?");
            
            Optional<ButtonType> result = alert.showAndWait();
            return result.isPresent() && result.get() == ButtonType.OK;
        }
        return true;
    }
    
    public void setPrimaryStage(Stage primaryStage) {
        this.primaryStage = primaryStage;
    }
}
