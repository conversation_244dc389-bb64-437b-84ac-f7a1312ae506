package com.drawing.patterns;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Implémentation de la stratégie de journalisation dans la console
 */
public class ConsoleLogger implements LoggingStrategy {
    
    private static final DateTimeFormatter TIMESTAMP_FORMAT = 
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private final boolean includeTimestamp;
    private final boolean colorOutput;
    
    public ConsoleLogger() {
        this(true, true);
    }
    
    public ConsoleLogger(boolean includeTimestamp, boolean colorOutput) {
        this.includeTimestamp = includeTimestamp;
        this.colorOutput = colorOutput;
    }
    
    @Override
    public void log(String message) {
        log(LogLevel.INFO, message);
    }
    
    @Override
    public void log(LogLevel level, String message) {
        String formattedMessage = formatMessage(level, message);
        
        if (colorOutput) {
            System.out.println(getColorCode(level) + formattedMessage + getResetCode());
        } else {
            System.out.println(formattedMessage);
        }
    }
    
    @Override
    public void log(LogLevel level, String message, Throwable throwable) {
        log(level, message + " - Exception: " + throwable.getMessage());
        if (level == LogLevel.ERROR || level == LogLevel.DEBUG) {
            throwable.printStackTrace();
        }
    }
    
    @Override
    public void close() {
        // Rien à fermer pour la console
        log(LogLevel.INFO, "Console logger fermé");
    }
    
    @Override
    public String getStrategyName() {
        return "Console";
    }
    
    @Override
    public boolean isAvailable() {
        return true; // La console est toujours disponible
    }
    
    private String formatMessage(LogLevel level, String message) {
        StringBuilder sb = new StringBuilder();
        
        if (includeTimestamp) {
            sb.append("[").append(LocalDateTime.now().format(TIMESTAMP_FORMAT)).append("] ");
        }
        
        sb.append("[").append(level.getName()).append("] ");
        sb.append(message);
        
        return sb.toString();
    }
    
    private String getColorCode(LogLevel level) {
        if (!colorOutput) {
            return "";
        }
        
        switch (level) {
            case DEBUG:
                return "\u001B[36m"; // Cyan
            case INFO:
                return "\u001B[32m"; // Vert
            case WARN:
                return "\u001B[33m"; // Jaune
            case ERROR:
                return "\u001B[31m"; // Rouge
            default:
                return "";
        }
    }
    
    private String getResetCode() {
        return colorOutput ? "\u001B[0m" : "";
    }
}
