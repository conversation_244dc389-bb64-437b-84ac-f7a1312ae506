package com.drawing.demo;

import com.drawing.model.PointSimple;
import com.drawing.patterns.ShapeFactorySimple;
import com.drawing.patterns.LoggingStrategySimple;
import com.drawing.patterns.CommandPatternSimple;

/**
 * Démonstration complète de l'application de dessin
 * Combine tous les design patterns implémentés
 */
public class DrawingAppDemo {
    
    /**
     * Simulateur d'application de dessin
     */
    public static class DrawingAppSimulator {
        private final CommandPatternSimple.SimpleDrawing drawing;
        private final CommandPatternSimple.CommandManager commandManager;
        private final LoggingStrategySimple.Logger logger;
        private ShapeFactorySimple.ShapeType currentShapeType;
        
        public DrawingAppSimulator(String drawingName) {
            this.drawing = new CommandPatternSimple.SimpleDrawing(drawingName);
            this.commandManager = new CommandPatternSimple.CommandManager();
            
            // Initialiser avec la stratégie de logging console
            LoggingStrategySimple.LogStrategy consoleStrategy = 
                new LoggingStrategySimple.ConsoleLogStrategy();
            this.logger = new LoggingStrategySimple.Logger(consoleStrategy);
            
            this.currentShapeType = ShapeFactorySimple.ShapeType.RECTANGLE;
            
            logger.log("Application de dessin initialisée");
            logger.logUserAction("Initialisation", "Nouveau dessin créé: " + drawingName);
        }
        
        public void setLoggingStrategy(LoggingStrategySimple.LogStrategy strategy) {
            logger.setStrategy(strategy);
            logger.logUserAction("Configuration", "Stratégie de logging changée vers: " + strategy.getStrategyName());
        }
        
        public void selectShapeType(ShapeFactorySimple.ShapeType shapeType) {
            this.currentShapeType = shapeType;
            logger.logUserAction("Sélection", "Type de forme sélectionné: " + shapeType.getDisplayName());
        }
        
        public void drawShape(PointSimple point1, PointSimple point2) {
            try {
                // Utiliser le Factory Pattern pour créer la forme
                ShapeFactorySimple.SimpleShape shape = 
                    ShapeFactorySimple.createShape(currentShapeType, point1, point2);
                
                // Utiliser le Command Pattern pour ajouter la forme
                CommandPatternSimple.Command addCommand = 
                    new CommandPatternSimple.AddShapeCommand(drawing, shape);
                
                commandManager.executeCommand(addCommand);
                
                logger.logUserAction("Dessin", 
                    String.format("Forme créée: %s de %s à %s", 
                                currentShapeType.getDisplayName(), point1, point2));
                
            } catch (Exception e) {
                logger.logError("Erreur lors de la création de la forme: " + e.getMessage());
            }
        }
        
        public void undo() {
            if (commandManager.canUndo()) {
                String description = commandManager.getUndoDescription();
                boolean success = commandManager.undo();
                if (success) {
                    logger.logUserAction("Annulation", "Commande annulée: " + description);
                }
            } else {
                logger.logWarning("Aucune action à annuler");
            }
        }
        
        public void redo() {
            if (commandManager.canRedo()) {
                String description = commandManager.getRedoDescription();
                boolean success = commandManager.redo();
                if (success) {
                    logger.logUserAction("Rétablissement", "Commande rétablie: " + description);
                }
            } else {
                logger.logWarning("Aucune action à rétablir");
            }
        }
        
        public void clearDrawing() {
            CommandPatternSimple.Command clearCommand = 
                new CommandPatternSimple.ClearDrawingCommand(drawing);
            commandManager.executeCommand(clearCommand);
            logger.logUserAction("Effacement", "Toutes les formes ont été supprimées");
        }
        
        public void showDrawing() {
            drawing.display();
        }
        
        public void showHistory() {
            commandManager.showHistory();
        }
        
        public void saveDrawing() {
            // Simulation de sauvegarde
            logger.logUserAction("Sauvegarde", "Dessin sauvegardé: " + drawing.getName());
            System.out.println("💾 Dessin sauvegardé avec succès!");
        }
        
        public void loadDrawing(String name) {
            // Simulation de chargement
            drawing.setName(name);
            logger.logUserAction("Chargement", "Dessin chargé: " + name);
            System.out.println("📂 Dessin chargé avec succès!");
        }
        
        public void close() {
            logger.logUserAction("Fermeture", "Application fermée");
            logger.close();
        }
        
        public String getDrawingName() {
            return drawing.getName();
        }
        
        public int getShapeCount() {
            return drawing.getShapeCount();
        }
    }
    
    /**
     * Scénario de démonstration
     */
    public static void runDemo() {
        System.out.println("🎨 === DÉMONSTRATION COMPLÈTE DE L'APPLICATION DE DESSIN ===");
        System.out.println();
        
        // 1. Initialisation de l'application
        System.out.println("📋 1. INITIALISATION DE L'APPLICATION");
        DrawingAppSimulator app = new DrawingAppSimulator("Mon Premier Dessin");
        System.out.println();
        
        // 2. Configuration du logging
        System.out.println("📋 2. CONFIGURATION DU LOGGING");
        LoggingStrategySimple.LogStrategy fileStrategy = 
            new LoggingStrategySimple.FileLogStrategy("logs/demo-complete.log");
        app.setLoggingStrategy(fileStrategy);
        System.out.println();
        
        // 3. Création de formes
        System.out.println("📋 3. CRÉATION DE FORMES");
        
        // Dessiner un rectangle
        app.selectShapeType(ShapeFactorySimple.ShapeType.RECTANGLE);
        app.drawShape(new PointSimple(1, 1), new PointSimple(5, 4));
        app.showDrawing();

        // Dessiner un rectangle
        app.selectShapeType(ShapeFactorySimple.ShapeType.RECTANGLE);
        app.drawShape(new PointSimple(3, 1), new PointSimple(5, 4));
        app.showDrawing();
        
        // Dessiner un cercle
        app.selectShapeType(ShapeFactorySimple.ShapeType.CIRCLE);
        app.drawShape(new PointSimple(3, 3), new PointSimple(6, 6));
        app.showDrawing();
        
        // Dessiner une ligne
        app.selectShapeType(ShapeFactorySimple.ShapeType.LINE);
        app.drawShape(new PointSimple(0, 0), new PointSimple(8, 6));
        app.showDrawing();
        System.out.println();
        
        // 4. Test des opérations undo/redo
        System.out.println("📋 4. TEST DES OPÉRATIONS UNDO/REDO");
        app.showHistory();
        
        System.out.println("\n🔄 Annulation de la dernière action:");
        app.undo();
        app.showDrawing();
        
        System.out.println("\n🔄 Annulation d'une autre action:");
        app.undo();
        app.showDrawing();
        
        System.out.println("\n🔄 Rétablissement d'une action:");
        app.redo();
        app.showDrawing();
        System.out.println();
        
        // 5. Ajout de nouvelles formes
        System.out.println("📋 5. AJOUT DE NOUVELLES FORMES");
        app.selectShapeType(ShapeFactorySimple.ShapeType.CIRCLE);
        app.drawShape(new PointSimple(7, 2), new PointSimple(9, 4));
        
        app.selectShapeType(ShapeFactorySimple.ShapeType.RECTANGLE);
        app.drawShape(new PointSimple(2, 6), new PointSimple(4, 8));
        app.showDrawing();
        System.out.println();
        
        // 6. Sauvegarde
        System.out.println("📋 6. SAUVEGARDE DU DESSIN");
        app.saveDrawing();
        System.out.println();
        
        // 7. Test de l'effacement
        System.out.println("📋 7. TEST DE L'EFFACEMENT");
        app.clearDrawing();
        app.showDrawing();
        
        System.out.println("\n🔄 Annulation de l'effacement:");
        app.undo();
        app.showDrawing();
        System.out.println();
        
        // 8. Changement de stratégie de logging
        System.out.println("📋 8. CHANGEMENT DE STRATÉGIE DE LOGGING");
        LoggingStrategySimple.LogStrategy memoryStrategy = 
            new LoggingStrategySimple.MemoryLogStrategy();
        app.setLoggingStrategy(memoryStrategy);
        
        // Quelques actions avec la nouvelle stratégie
        app.selectShapeType(ShapeFactorySimple.ShapeType.LINE);
        app.drawShape(new PointSimple(1, 8), new PointSimple(8, 1));
        
        // Afficher les logs en mémoire
        System.out.println("\n📝 Logs capturés en mémoire:");
        ((LoggingStrategySimple.MemoryLogStrategy) memoryStrategy).printLogs();
        System.out.println();
        
        // 9. Simulation de chargement
        System.out.println("📋 9. SIMULATION DE CHARGEMENT");
        app.loadDrawing("Dessin Chargé");
        app.showDrawing();
        System.out.println();
        
        // 10. Historique final
        System.out.println("📋 10. HISTORIQUE FINAL DES COMMANDES");
        app.showHistory();
        System.out.println();
        
        // 11. Statistiques finales
        System.out.println("📋 11. STATISTIQUES FINALES");
        System.out.println("Nom du dessin: " + app.getDrawingName());
        System.out.println("Nombre de formes: " + app.getShapeCount());
        System.out.println();
        
        // 12. Fermeture
        System.out.println("📋 12. FERMETURE DE L'APPLICATION");
        app.close();
        
        System.out.println();
        System.out.println("🎉 === DÉMONSTRATION TERMINÉE AVEC SUCCÈS ===");
        System.out.println();
        System.out.println("✅ Design Patterns démontrés:");
        System.out.println("   • Factory Pattern - Création des formes géométriques");
        System.out.println("   • Strategy Pattern - Stratégies de journalisation");
        System.out.println("   • Command Pattern - Gestion des actions avec undo/redo");
        System.out.println("   • MVC Pattern - Séparation des responsabilités");
        System.out.println();
        System.out.println("📁 Fichiers générés:");
        System.out.println("   • logs/demo-complete.log - Logs de l'application");
        System.out.println("   • target/classes/ - Classes compilées");
    }
    
    /**
     * Point d'entrée de la démonstration
     */
    public static void main(String[] args) {
        runDemo();
    }
}
