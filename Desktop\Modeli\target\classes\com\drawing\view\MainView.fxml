<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.drawing.controller.MainController">
   <top>
      <VBox>
         <!-- Menu Bar -->
         <MenuBar fx:id="menuBar">
            <menus>
               <Menu fx:id="fileMenu" mnemonicParsing="false" text="Fichier">
                  <items>
                     <MenuItem mnemonicParsing="false" onAction="#handleNewDrawing" text="Nouveau" />
                     <SeparatorMenuItem mnemonicParsing="false" />
                     <MenuItem mnemonicParsing="false" onAction="#handleSaveDrawing" text="Sauvegarder" />
                     <MenuItem mnemonicParsing="false" onAction="#handleLoadDrawing" text="Charger" />
                     <SeparatorMenuItem mnemonicParsing="false" />
                     <MenuItem mnemonicParsing="false" onAction="#handleExit" text="Quitter" />
                  </items>
               </Menu>
               <Menu fx:id="editMenu" mnemonicParsing="false" text="Édition">
                  <items>
                     <MenuItem mnemonicParsing="false" onAction="#handleUndo" text="Annuler" />
                     <MenuItem mnemonicParsing="false" onAction="#handleRedo" text="Rétablir" />
                     <SeparatorMenuItem mnemonicParsing="false" />
                     <MenuItem mnemonicParsing="false" onAction="#handleClear" text="Effacer tout" />
                  </items>
               </Menu>
               <Menu fx:id="viewMenu" mnemonicParsing="false" text="Affichage">
                  <items>
                     <MenuItem mnemonicParsing="false" text="Zoom +" />
                     <MenuItem mnemonicParsing="false" text="Zoom -" />
                     <MenuItem mnemonicParsing="false" text="Zoom 100%" />
                  </items>
               </Menu>
               <Menu fx:id="helpMenu" mnemonicParsing="false" text="Aide">
                  <items>
                     <MenuItem mnemonicParsing="false" text="À propos" />
                  </items>
               </Menu>
            </menus>
         </MenuBar>
         
         <!-- Tool Bar -->
         <ToolBar fx:id="toolBar">
            <items>
               <!-- Formes -->
               <Label text="Formes:" />
               <Separator orientation="VERTICAL" />
               
               <ToggleGroup fx:id="shapeToggleGroup" />
               <RadioButton fx:id="rectangleButton" mnemonicParsing="false" text="Rectangle" toggleGroup="$shapeToggleGroup">
                  <tooltip>
                     <Tooltip text="Dessiner des rectangles" />
                  </tooltip>
               </RadioButton>
               <RadioButton fx:id="circleButton" mnemonicParsing="false" text="Cercle" toggleGroup="$shapeToggleGroup">
                  <tooltip>
                     <Tooltip text="Dessiner des cercles" />
                  </tooltip>
               </RadioButton>
               <RadioButton fx:id="lineButton" mnemonicParsing="false" text="Ligne" toggleGroup="$shapeToggleGroup">
                  <tooltip>
                     <Tooltip text="Dessiner des lignes" />
                  </tooltip>
               </RadioButton>
               
               <Separator orientation="VERTICAL" />
               
               <!-- Couleur -->
               <Label text="Couleur:" />
               <ColorPicker fx:id="colorPicker">
                  <tooltip>
                     <Tooltip text="Choisir la couleur de dessin" />
                  </tooltip>
               </ColorPicker>
               
               <Separator orientation="VERTICAL" />
               
               <!-- Actions -->
               <Button fx:id="undoButton" mnemonicParsing="false" onAction="#handleUndo" text="↶">
                  <tooltip>
                     <Tooltip text="Annuler la dernière action" />
                  </tooltip>
               </Button>
               <Button fx:id="redoButton" mnemonicParsing="false" onAction="#handleRedo" text="↷">
                  <tooltip>
                     <Tooltip text="Rétablir la dernière action annulée" />
                  </tooltip>
               </Button>
               <Button fx:id="clearButton" mnemonicParsing="false" onAction="#handleClear" text="🗑">
                  <tooltip>
                     <Tooltip text="Effacer tout le dessin" />
                  </tooltip>
               </Button>
               
               <Separator orientation="VERTICAL" />
               
               <!-- Logging Strategy -->
               <Label text="Journalisation:" />
               <ComboBox fx:id="loggingStrategyCombo" prefWidth="150.0">
                  <tooltip>
                     <Tooltip text="Choisir la stratégie de journalisation" />
                  </tooltip>
               </ComboBox>
            </items>
         </ToolBar>
      </VBox>
   </top>
   
   <center>
      <!-- Zone de dessin -->
      <Pane fx:id="canvasContainer" style="-fx-background-color: #f0f0f0;">
         <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
         </padding>
      </Pane>
   </center>
   
   <bottom>
      <!-- Barre de statut -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" style="-fx-background-color: #e0e0e0; -fx-padding: 5;">
         <children>
            <Label fx:id="statusLabel" text="Prêt" />
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="coordinatesLabel" text="X: 0, Y: 0" />
         </children>
      </HBox>
   </bottom>
</BorderPane>
