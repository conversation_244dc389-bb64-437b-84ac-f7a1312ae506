@echo off
echo === Compilation de l'application de dessin geometrique ===

REM Verifier si Java est installe
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Erreur: Java n'est pas installe ou n'est pas dans le PATH
    pause
    exit /b 1
)

REM Creer les repertoires necessaires
if not exist "target\classes" mkdir "target\classes"
if not exist "data" mkdir "data"
if not exist "logs" mkdir "logs"

echo Compilation des sources Java...

REM Compiler les sources (necessite JavaFX dans le classpath)
javac -d target\classes ^
      --module-path "C:\Program Files\Java\javafx-sdk-19\lib" ^
      --add-modules javafx.controls,javafx.fxml ^
      -cp "lib\*" ^
      src\main\java\module-info.java ^
      src\main\java\com\drawing\*.java ^
      src\main\java\com\drawing\controller\*.java ^
      src\main\java\com\drawing\model\*.java ^
      src\main\java\com\drawing\patterns\*.java ^
      src\main\java\com\drawing\service\*.java ^
      src\main\java\com\drawing\view\*.java

if %errorlevel% neq 0 (
    echo Erreur lors de la compilation
    pause
    exit /b 1
)

REM Copier les ressources
echo Copie des ressources...
xcopy /E /I /Y "src\main\resources" "target\classes" >nul

echo Compilation terminee avec succes!
echo.
echo Pour executer l'application, utilisez: run.bat
pause
