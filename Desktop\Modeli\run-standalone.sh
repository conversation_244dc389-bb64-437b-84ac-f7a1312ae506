#!/bin/bash

echo "🎨 === APPLICATION DE DESSIN AUTONOME ==="
echo

# Vérifier si Java est installé
if ! command -v java &> /dev/null; then
    echo "❌ Erreur: Java n'est pas installé ou n'est pas dans le PATH"
    exit 1
fi

# Afficher la version Java
echo "☕ Version Java:"
java --version | head -1

# Créer les répertoires nécessaires
mkdir -p target/classes

echo
echo "📦 Compilation de l'application autonome..."

# Compiler l'application autonome (sans dépendances)
javac -d target/classes \
      src/main/java/com/drawing/standalone/StandaloneDrawingApp.java

if [ $? -ne 0 ]; then
    echo "❌ Erreur de compilation"
    exit 1
fi

echo "✅ Compilation réussie!"
echo
echo "🚀 Lancement de l'application..."
echo "   📱 Une fenêtre graphique va s'ouvrir"
echo "   🎯 Fonctionnalités disponibles:"
echo "      • Des<PERSON><PERSON> des formes (Rectangle, Cercle, Ligne)"
echo "      • Changer de couleur"
echo "      • Annuler/Rétablir (Undo/Redo)"
echo "      • Démonstration automatique"
echo "      • Design Patterns intégrés"
echo

# Exécuter l'application
java -cp target/classes com.drawing.standalone.StandaloneDrawingApp

echo
echo "🎉 Application fermée!"
