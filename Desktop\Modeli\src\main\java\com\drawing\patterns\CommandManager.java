package com.drawing.patterns;

import java.util.ArrayList;
import java.util.List;

/**
 * Gestionnaire des commandes pour supporter undo/redo
 * Utilise le pattern Command
 */
public class CommandManager {
    
    private final List<Command> history;
    private int currentIndex;
    private final int maxHistorySize;
    
    public CommandManager() {
        this(100); // Historique de 100 commandes par défaut
    }
    
    public CommandManager(int maxHistorySize) {
        this.history = new ArrayList<>();
        this.currentIndex = -1;
        this.maxHistorySize = maxHistorySize;
    }
    
    /**
     * Exécute une commande et l'ajoute à l'historique
     */
    public void executeCommand(Command command) {
        // Supprimer toutes les commandes après l'index actuel (pour le redo)
        if (currentIndex < history.size() - 1) {
            history.subList(currentIndex + 1, history.size()).clear();
        }
        
        // Exécuter la commande
        command.execute();
        
        // Ajouter à l'historique
        history.add(command);
        currentIndex++;
        
        // Limiter la taille de l'historique
        if (history.size() > maxHistorySize) {
            history.remove(0);
            currentIndex--;
        }
    }
    
    /**
     * Annule la dernière commande
     */
    public boolean undo() {
        if (canUndo()) {
            Command command = history.get(currentIndex);
            command.undo();
            currentIndex--;
            return true;
        }
        return false;
    }
    
    /**
     * Refait la prochaine commande
     */
    public boolean redo() {
        if (canRedo()) {
            currentIndex++;
            Command command = history.get(currentIndex);
            command.redo();
            return true;
        }
        return false;
    }
    
    /**
     * Vérifie si on peut annuler
     */
    public boolean canUndo() {
        return currentIndex >= 0 && 
               currentIndex < history.size() && 
               history.get(currentIndex).canUndo();
    }
    
    /**
     * Vérifie si on peut refaire
     */
    public boolean canRedo() {
        return currentIndex + 1 < history.size() && 
               history.get(currentIndex + 1).canRedo();
    }
    
    /**
     * Retourne la description de la prochaine commande à annuler
     */
    public String getUndoDescription() {
        if (canUndo()) {
            return history.get(currentIndex).getDescription();
        }
        return null;
    }
    
    /**
     * Retourne la description de la prochaine commande à refaire
     */
    public String getRedoDescription() {
        if (canRedo()) {
            return history.get(currentIndex + 1).getDescription();
        }
        return null;
    }
    
    /**
     * Efface l'historique
     */
    public void clearHistory() {
        history.clear();
        currentIndex = -1;
    }
    
    /**
     * Retourne la taille de l'historique
     */
    public int getHistorySize() {
        return history.size();
    }
    
    /**
     * Retourne l'index actuel dans l'historique
     */
    public int getCurrentIndex() {
        return currentIndex;
    }
    
    /**
     * Retourne une copie de l'historique des commandes
     */
    public List<String> getHistoryDescriptions() {
        List<String> descriptions = new ArrayList<>();
        for (int i = 0; i < history.size(); i++) {
            String desc = history.get(i).getDescription();
            if (i <= currentIndex) {
                descriptions.add(desc);
            } else {
                descriptions.add("(" + desc + ")"); // Commandes annulées entre parenthèses
            }
        }
        return descriptions;
    }
}
