#!/bin/bash

echo "🎨 === APPLICATION DE DESSIN SWING ==="
echo

# Vérifier si Java est installé
if ! command -v java &> /dev/null; then
    echo "❌ Erreur: Java n'est pas installé ou n'est pas dans le PATH"
    exit 1
fi

# Afficher la version Java
echo "☕ Version Java:"
java --version | head -1

# Créer les répertoires nécessaires
mkdir -p target/classes
mkdir -p logs

echo
echo "📦 Compilation des classes Java..."

# Compiler les classes nécessaires
javac -d target/classes \
      src/main/java/com/drawing/model/Point.java \
      src/main/java/com/drawing/patterns/ShapeFactorySimple.java \
      src/main/java/com/drawing/SwingDrawingApp.java

if [ $? -ne 0 ]; then
    echo "❌ Erreur de compilation"
    exit 1
fi

echo "✅ Compilation réussie!"
echo
echo "🚀 Lancement de l'application Swing..."
echo "   (Une fenêtre graphique va s'ouvrir)"
echo

# Exécuter l'application Swing
java -cp target/classes com.drawing.SwingDrawingApp

echo
echo "🎉 Application fermée!"
