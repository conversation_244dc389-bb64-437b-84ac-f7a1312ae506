package com.drawing.model;

import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

/**
 * Implémentation d'un rectangle
 */
public class Rectangle implements Shape {
    private final Point topLeft;
    private final double width;
    private final double height;
    private Color color;

    public Rectangle(Point topLeft, double width, double height, Color color) {
        this.topLeft = topLeft;
        this.width = Math.abs(width);
        this.height = Math.abs(height);
        this.color = color != null ? color : Color.BLACK;
    }

    public Rectangle(Point topLeft, double width, double height) {
        this(topLeft, width, height, Color.BLACK);
    }

    @Override
    public void draw(GraphicsContext gc) {
        gc.setStroke(color);
        gc.setLineWidth(2.0);
        gc.strokeRect(topLeft.getX(), topLeft.getY(), width, height);
    }

    @Override
    public String getType() {
        return "rectangle";
    }

    @Override
    public Color getColor() {
        return color;
    }

    @Override
    public void setColor(Color color) {
        this.color = color;
    }

    @Override
    public boolean contains(Point point) {
        return point.getX() >= topLeft.getX() && 
               point.getX() <= topLeft.getX() + width &&
               point.getY() >= topLeft.getY() && 
               point.getY() <= topLeft.getY() + height;
    }

    @Override
    public Bounds getBounds() {
        return new Bounds(topLeft.getX(), topLeft.getY(), 
                         topLeft.getX() + width, topLeft.getY() + height);
    }

    @Override
    public Shape clone() {
        return new Rectangle(topLeft, width, height, color);
    }



    // Getters
    public Point getTopLeft() {
        return topLeft;
    }

    public double getWidth() {
        return width;
    }

    public double getHeight() {
        return height;
    }

    @Override
    public String toString() {
        return String.format("Rectangle[topLeft=%s, width=%.2f, height=%.2f, color=%s]", 
                           topLeft, width, height, color);
    }
}
