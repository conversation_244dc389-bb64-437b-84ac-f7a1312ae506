package com.drawing.patterns;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Implémentation de la stratégie de journalisation dans un fichier
 */
public class FileLogger implements LoggingStrategy {
    
    private static final DateTimeFormatter TIMESTAMP_FORMAT = 
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private final String logFilePath;
    private BufferedWriter writer;
    private boolean isAvailable;
    
    public FileLogger(String logFilePath) {
        this.logFilePath = logFilePath;
        this.isAvailable = initializeWriter();
    }
    
    public FileLogger() {
        this("logs/drawing-app.log");
    }
    
    private boolean initializeWriter() {
        try {
            // Créer le répertoire si nécessaire
            Path logPath = Paths.get(logFilePath);
            Path parentDir = logPath.getParent();
            if (parentDir != null && !Files.exists(parentDir)) {
                Files.createDirectories(parentDir);
            }
            
            // Ouvrir le fichier en mode append
            this.writer = new BufferedWriter(new FileWriter(logFilePath, true));
            
            // Écrire un message de démarrage
            writeToFile(formatMessage(LogLevel.INFO, "=== Session de journalisation démarrée ==="));
            
            return true;
        } catch (IOException e) {
            System.err.println("Erreur lors de l'initialisation du FileLogger: " + e.getMessage());
            return false;
        }
    }
    
    @Override
    public void log(String message) {
        log(LogLevel.INFO, message);
    }
    
    @Override
    public void log(LogLevel level, String message) {
        if (!isAvailable) {
            return;
        }
        
        String formattedMessage = formatMessage(level, message);
        writeToFile(formattedMessage);
    }
    
    @Override
    public void log(LogLevel level, String message, Throwable throwable) {
        if (!isAvailable) {
            return;
        }
        
        String formattedMessage = formatMessage(level, message + " - Exception: " + throwable.getMessage());
        writeToFile(formattedMessage);
        
        // Écrire la stack trace pour les erreurs
        if (level == LogLevel.ERROR) {
            writeToFile("Stack trace:");
            for (StackTraceElement element : throwable.getStackTrace()) {
                writeToFile("  " + element.toString());
            }
        }
    }
    
    @Override
    public void close() {
        if (writer != null) {
            try {
                writeToFile(formatMessage(LogLevel.INFO, "=== Session de journalisation terminée ==="));
                writer.close();
                isAvailable = false;
            } catch (IOException e) {
                System.err.println("Erreur lors de la fermeture du FileLogger: " + e.getMessage());
            }
        }
    }
    
    @Override
    public String getStrategyName() {
        return "Fichier (" + logFilePath + ")";
    }
    
    @Override
    public boolean isAvailable() {
        return isAvailable;
    }
    
    private String formatMessage(LogLevel level, String message) {
        return String.format("[%s] [%s] %s", 
                           LocalDateTime.now().format(TIMESTAMP_FORMAT),
                           level.getName(),
                           message);
    }
    
    private synchronized void writeToFile(String message) {
        if (writer == null) {
            return;
        }
        
        try {
            writer.write(message);
            writer.newLine();
            writer.flush(); // Assure que le message est écrit immédiatement
        } catch (IOException e) {
            System.err.println("Erreur lors de l'écriture dans le fichier de log: " + e.getMessage());
            isAvailable = false;
        }
    }
    
    public String getLogFilePath() {
        return logFilePath;
    }
}
