#!/bin/bash

echo "🎨 === COMPILATION ET EXÉCUTION JAVAFX ==="
echo

# Vérifier si Java est installé
if ! command -v java &> /dev/null; then
    echo "❌ Erreur: Java n'est pas installé ou n'est pas dans le PATH"
    exit 1
fi

# Afficher la version Java
echo "☕ Version Java:"
java --version | head -1

# Créer les répertoires nécessaires
mkdir -p target/classes
mkdir -p logs

echo
echo "📦 Compilation des classes Java..."

# Compiler les classes de base sans module-info pour éviter les problèmes
javac -d target/classes \
      --module-path /usr/share/openjfx/lib 2>/dev/null || \
javac -d target/classes \
      --module-path /usr/lib/jvm/javafx/lib 2>/dev/null || \
javac -d target/classes \
      src/main/java/com/drawing/model/Point.java \
      src/main/java/com/drawing/patterns/ShapeFactorySimple.java \
      src/main/java/com/drawing/SimpleDrawingApp.java 2>/dev/null

if [ $? -eq 0 ]; then
    echo "✅ Compilation réussie!"
else
    echo "⚠️  Compilation avec JavaFX échouée, essai sans JavaFX..."
    
    # Essayer de compiler sans JavaFX pour tester la structure
    javac -d target/classes \
          src/main/java/com/drawing/model/Point.java \
          src/main/java/com/drawing/patterns/ShapeFactorySimple.java
    
    if [ $? -eq 0 ]; then
        echo "✅ Classes de base compilées avec succès!"
        echo "❌ JavaFX n'est pas disponible sur ce système"
        echo
        echo "📋 Pour installer JavaFX:"
        echo "  • Ubuntu/Debian: sudo apt install openjfx"
        echo "  • Fedora: sudo dnf install java-openjfx"
        echo "  • macOS: brew install openjfx"
        echo "  • Windows: Télécharger depuis https://openjfx.io/"
        echo
        echo "🔄 Exécution de la démonstration console à la place..."
        java -cp target/classes com.drawing.patterns.ShapeFactorySimple
        exit 0
    else
        echo "❌ Erreur de compilation"
        exit 1
    fi
fi

echo
echo "🚀 Lancement de l'application JavaFX..."

# Essayer différents chemins pour JavaFX
JAVAFX_PATHS=(
    "/usr/share/openjfx/lib"
    "/usr/lib/jvm/javafx/lib"
    "/opt/javafx/lib"
    "/usr/local/lib/javafx/lib"
    "$HOME/javafx/lib"
)

JAVAFX_FOUND=false

for path in "${JAVAFX_PATHS[@]}"; do
    if [ -d "$path" ]; then
        echo "✅ JavaFX trouvé dans: $path"
        JAVAFX_PATH="$path"
        JAVAFX_FOUND=true
        break
    fi
done

if [ "$JAVAFX_FOUND" = true ]; then
    echo "🎯 Exécution avec JavaFX..."
    java --module-path "$JAVAFX_PATH" \
         --add-modules javafx.controls,javafx.fxml \
         -cp target/classes \
         com.drawing.SimpleDrawingApp
else
    echo "⚠️  JavaFX non trouvé dans les chemins standards"
    echo "🔄 Tentative d'exécution sans module-path..."
    
    # Essayer sans module-path (si JavaFX est dans le JDK)
    java -cp target/classes com.drawing.SimpleDrawingApp 2>/dev/null
    
    if [ $? -ne 0 ]; then
        echo "❌ Impossible d'exécuter l'application JavaFX"
        echo
        echo "📋 Solutions possibles:"
        echo "1. Installer JavaFX: sudo apt install openjfx"
        echo "2. Télécharger JavaFX SDK depuis https://openjfx.io/"
        echo "3. Utiliser un JDK qui inclut JavaFX"
        echo
        echo "🔄 Exécution de la démonstration console à la place..."
        java -cp target/classes com.drawing.patterns.ShapeFactorySimple
    fi
fi

echo
echo "🎉 Exécution terminée!"
