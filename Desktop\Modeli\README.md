# Application de Dessin Géométrique

Une application JavaFX permettant de dessiner des formes géométriques avec une architecture basée sur des design patterns.

## Fonctionnalités

### Fonctionnalités principales
- ✅ Sélection de formes géométriques (Rectangle, Cercle, Ligne)
- ✅ Dessin interactif sur une zone de dessin
- ✅ Sauvegarde et chargement des dessins en base de données
- ✅ Journalisation des actions utilisateur
- ✅ Système d'annulation/rétablissement (Undo/Redo)

### Stratégies de journalisation
- ✅ Console
- ✅ Fichier texte
- ✅ Base de données SQLite

## Architecture et Design Patterns

### Design Patterns utilisés

1. **MVC (Model-View-Controller)**
   - `model/` : Classes métier (Drawing, Shape, Point, etc.)
   - `view/` : Interface utilisateur (FXML, DrawingCanvas)
   - `controller/` : Logique de contrôle (MainController)

2. **Factory Pattern**
   - `ShapeFactory` : Création centralisée des formes géométriques

3. **Strategy Pattern**
   - `LoggingStrategy` : Interface pour les stratégies de journalisation
   - `ConsoleLogger`, `FileLogger`, `DatabaseLogger` : Implémentations

4. **Command Pattern**
   - `Command` : Interface pour les commandes
   - `DrawShapeCommand` : Commande pour dessiner une forme
   - `CommandManager` : Gestionnaire d'historique pour undo/redo

5. **Singleton Pattern**
   - `DatabaseManager` : Gestion unique de la connexion à la base

6. **Observer Pattern**
   - Événements JavaFX pour la communication entre composants

## Structure du projet

```
src/main/java/com/drawing/
├── DrawingApp.java                 # Point d'entrée de l'application
├── controller/
│   └── MainController.java        # Contrôleur principal
├── model/
│   ├── Drawing.java               # Modèle d'un dessin complet
│   ├── Shape.java                 # Interface pour les formes
│   ├── Rectangle.java             # Implémentation rectangle
│   ├── Circle.java                # Implémentation cercle
│   ├── Line.java                  # Implémentation ligne
│   ├── Point.java                 # Classe pour les coordonnées
│   └── Bounds.java                # Limites rectangulaires
├── patterns/
│   ├── ShapeFactory.java          # Factory pour les formes
│   ├── LoggingStrategy.java       # Interface stratégie logging
│   ├── ConsoleLogger.java         # Logging console
│   ├── FileLogger.java            # Logging fichier
│   ├── DatabaseLogger.java        # Logging base de données
│   ├── DatabaseManager.java       # Singleton base de données
│   ├── Command.java               # Interface commande
│   ├── DrawShapeCommand.java      # Commande dessiner forme
│   └── CommandManager.java        # Gestionnaire commandes
├── service/
│   ├── LoggingService.java        # Service de journalisation
│   └── DrawingService.java        # Service sauvegarde/chargement
└── view/
    └── DrawingCanvas.java         # Zone de dessin personnalisée

src/main/resources/
├── com/drawing/view/
│   └── MainView.fxml              # Interface utilisateur
└── logback.xml                   # Configuration logging
```

## Prérequis

- Java 17 ou supérieur
- JavaFX 19
- Maven 3.6+

## Installation et exécution

### 1. Cloner le projet
```bash
git clone <url-du-projet>
cd geometric-drawing-app
```

### 2. Compiler le projet
```bash
mvn clean compile
```

### 3. Exécuter l'application
```bash
mvn javafx:run
```

### 4. Créer un JAR exécutable
```bash
mvn clean package
```

## Utilisation

### Interface utilisateur

1. **Barre d'outils** :
   - Sélection de la forme (Rectangle, Cercle, Ligne)
   - Sélecteur de couleur
   - Boutons Annuler/Rétablir
   - Bouton Effacer
   - Sélecteur de stratégie de journalisation

2. **Zone de dessin** :
   - Clic et glissement pour dessiner une forme
   - Prévisualisation en temps réel

3. **Menu** :
   - Fichier : Nouveau, Sauvegarder, Charger, Quitter
   - Édition : Annuler, Rétablir, Effacer tout

### Dessiner une forme

1. Sélectionner le type de forme dans la barre d'outils
2. Choisir une couleur (optionnel)
3. Cliquer et glisser sur la zone de dessin
4. Relâcher pour créer la forme

### Sauvegarder/Charger

- **Sauvegarder** : Menu Fichier > Sauvegarder
- **Charger** : Menu Fichier > Charger (liste des dessins disponibles)

### Journalisation

Changer la stratégie dans le menu déroulant :
- **Console** : Messages dans la console de l'application
- **Fichier** : Messages dans `logs/drawing-app.log`
- **Base de données** : Messages dans la table `logs` de SQLite

## Base de données

L'application utilise SQLite avec les tables :

- `drawings` : Stockage des dessins (JSON)
- `logs` : Journalisation des actions

Base de données créée automatiquement dans `data/drawing_app.db`

## Tests

```bash
mvn test
```

## Développement

### Ajouter une nouvelle forme

1. Créer une classe implémentant `Shape`
2. Ajouter le type dans `ShapeFactory.ShapeType`
3. Implémenter la création dans `ShapeFactory`
4. Ajouter l'annotation JSON dans `Shape.java`

### Ajouter une nouvelle stratégie de logging

1. Créer une classe implémentant `LoggingStrategy`
2. Ajouter le type dans `LoggingService.StrategyType`
3. Initialiser dans `LoggingService.initializeStrategies()`

## Licence

Ce projet est développé dans un cadre éducatif.
